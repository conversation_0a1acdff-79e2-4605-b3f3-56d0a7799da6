package config

const (
	// 성공 코드
	ErrSuccess = 0

	ErrFailedToShutdownServer     = 1000
	ErrFailedToLoadEnv            = 1001
	ErrFailedToOpenConfig         = 1002
	ErrFailedToDecodeConfig       = 1003
	ErrInvalidPortEnv             = 1004
	ErrFailedToCreateLogDir       = 1005
	ErrFailedToBuildLogger        = 1006
	ErrFailedToSyncLogger         = 1007
	ErrRedisConnectionFailed      = 1008
	ErrFailedToCreateConsulClient = 1009

	ErrFailedToRegisterService  = 1010
	ErrFailedToGetServices      = 1011
	ErrFailedToCreateSession    = 1012
	ErrFailedToProcessLogin     = 1013
	ErrFailedToSyncWithRedis    = 1014
	ErrFailedToInitializeServer = 1015
	ErrFailedToStartServer      = 1016
	ErrFailedToLoadCertificates = 1017
	ErrMySQLConnectionFailed    = 1018
	ErrMySQLQueryFailed         = 1019

	ErrFailedToDeleteGateServer = 1020

	ErrInvalidRequestBody    = 205
	ErrMissingRequiredField  = 206
	ErrNoAvailableGateServer = 207
	ErrSteamAPIError         = 208
	ErrSteamAuthFailed       = 209
	ErrInvalidSession        = 210
	ErrUnknownSessionStatus  = 211
	ErrFailedToGetGateServer = 212
	ErrInvalidWorld          = 213
	ErrKGAPIError            = 214
	ErrKGAuthFailed          = 215
	ErrInvalidURL            = 216
	ErrInvalidLoginType      = 217
	ErrWorldUnderMaintenance = 218
)

var ErrorMessages = map[int]string{
	ErrSuccess: "Success",

	ErrFailedToShutdownServer:     "Failed to shutdown server",
	ErrFailedToLoadEnv:            "Failed to load environment variables",
	ErrFailedToOpenConfig:         "Failed to open configuration file",
	ErrFailedToDecodeConfig:       "Failed to decode configuration",
	ErrInvalidPortEnv:             "Invalid PORT environment variable",
	ErrFailedToCreateLogDir:       "Failed to create log directory",
	ErrFailedToBuildLogger:        "Failed to build logger",
	ErrFailedToSyncLogger:         "Failed to sync logger",
	ErrRedisConnectionFailed:      "Redis connection failed",
	ErrFailedToCreateConsulClient: "Failed to create Consul client",

	ErrFailedToRegisterService:  "Failed to register service",
	ErrFailedToGetServices:      "Failed to get services",
	ErrFailedToCreateSession:    "Failed to create temporary session",
	ErrFailedToProcessLogin:     "Failed to process login",
	ErrFailedToSyncWithRedis:    "Failed to sync with Redis",
	ErrFailedToInitializeServer: "Failed to initialize server",
	ErrFailedToStartServer:      "Failed to start server",
	ErrFailedToLoadCertificates: "Failed to load certificates",
	ErrMySQLConnectionFailed:    "MySQL connection failed",
	ErrMySQLQueryFailed:         "MySQL query failed",

	ErrFailedToDeleteGateServer: "Failed to delete gate server",

	ErrInvalidRequestBody:    "Invalid request body",
	ErrMissingRequiredField:  "Missing required field",
	ErrNoAvailableGateServer: "No available gate server",
	ErrSteamAPIError:         "Steam API error",
	ErrSteamAuthFailed:       "Steam authentication failed",
	ErrInvalidSession:        "Invalid session",
	ErrUnknownSessionStatus:  "Unknown session status",
	ErrFailedToGetGateServer: "Failed to get gate server",
	ErrInvalidWorld:          "Invalid world",
	ErrKGAPIError:            "KG API error",
	ErrKGAuthFailed:          "KG authentication failed",
	ErrInvalidURL:            "Invalid URL",
	ErrInvalidLoginType:      "Invalid login type",
	ErrWorldUnderMaintenance: "World is under maintenance",
}
