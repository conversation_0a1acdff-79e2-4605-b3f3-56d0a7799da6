package agent

func <PERSON><PERSON>(process map[string]any) *Packet {
	payload := map[string]any{
		"payload_type": "JSON_PAYLOAD_TYPE_PROCESS_HELLO",
		"payload": map[string]any{
			"process": process,
		},
	}

	packet := CreateSendPacket("PACKET_FREE_FORMAT_JSON")
	packet.WriteJson(payload)
	return packet
}

func CreateChangeStatus(status string) *Packet {
	payload := map[string]any{
		"payload_type": "JSON_PAYLOAD_TYPE_PROCESS_STATUS_CHANGED",
		"payload": map[string]any{
			"changed": status,
		},
	}

	packet := CreateSendPacket("PACKET_FREE_FORMAT_JSON")
	packet.WriteJson(payload)
	return packet
}

func CreateMessageToLocalAgent(message string) *Packet {
	payload := map[string]any{
		"payload_type": "JSON_PAYLOAD_TYPE_MESSAGE_TO_LOCAL_AGENT",
		"payload": map[string]any{
			"message": message,
		},
	}

	packet := CreateSendPacket("PACKET_FREE_FORMAT_JSON")
	packet.WriteJson(payload)
	return packet
}

func CreateFrontServerInfo(process map[string]any, module map[string]any) *Packet {
	payload := map[string]any{
		"payload_type": "JSON_PAYLOAD_TYPE_FRONT_SERVER_INFO",
		"payload": map[string]any{
			"front_server_process": map[string]any{
				"process": process,
				"module":  module,
			},
		},
	}

	packet := CreateSendPacket("PACKET_FREE_FORMAT_JSON")
	packet.WriteJson(payload)
	return packet
}
