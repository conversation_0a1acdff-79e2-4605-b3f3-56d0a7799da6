package mysqlutil

import (
	"context"
	"database/sql"
	"fmt"
	"sync"
	"time"

	"NSFrontServer/config"
	"NSFrontServer/logger"

	_ "github.com/go-sql-driver/mysql"
	"go.uber.org/zap"
)

const (
	// MySQL 연결 관련 상수
	mysqlConnectTimeout   = 30 * time.Second  // 연결 타임아웃 증가
	mysqlOperationTimeout = 120 * time.Second // 작업 타임아웃 증가
	maxRetries            = 5                 // 재시도 횟수 증가
	retryDelay            = 200 * time.Millisecond
)

// Client MySQL 클라이언트 구조체
type Client struct {
	db  *sql.DB
	cfg *config.Config
	mu  sync.RWMutex
}

// NewClient 새로운 MySQL 클라이언트 생성
func NewClient(cfg *config.Config) (*Client, int) {
	client := &Client{
		cfg: cfg,
	}
	if err := client.connect(); err != nil {
		logger.Log.Error("MySQL connection failed", zap.Error(err))
		return nil, config.ErrMySQLConnectionFailed
	}
	return client, config.ErrSuccess
}

// connect MySQL 데이터베이스에 연결
func (c *Client) connect() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	// DSN 형식: username:password@tcp(host:port)/dbname
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?parseTime=true&loc=Local&charset=utf8mb4&collation=utf8mb4_unicode_ci",
		c.cfg.Common.CommonDB.ID,
		c.cfg.Common.CommonDB.Password,
		c.cfg.Common.CommonDB.Host,
		c.cfg.Common.CommonDB.Port,
		c.cfg.Common.CommonDB.DBName)

	db, err := sql.Open("mysql", dsn)
	if err != nil {
		return fmt.Errorf("failed to open MySQL connection: %w", err)
	}

	// 연결 설정
	db.SetMaxOpenConns(c.cfg.Common.CommonDB.PoolCount)
	db.SetMaxIdleConns(c.cfg.Common.CommonDB.PoolCount / 2)
	db.SetConnMaxLifetime(1 * time.Hour)
	db.SetConnMaxIdleTime(30 * time.Minute)

	// 커넥션 풀 고갈시 대기 시간 설정 (무한 대기 방지)
	// 참고: SetConnMaxIdleTime은 Go 1.15+에서 지원

	// 연결 테스트
	ctx, cancel := context.WithTimeout(context.Background(), mysqlConnectTimeout)
	defer cancel()

	if err := db.PingContext(ctx); err != nil {
		db.Close()
		return fmt.Errorf("failed to ping MySQL server: %w", err)
	}

	// 기존 연결이 있으면 닫기
	if c.db != nil {
		c.db.Close()
	}

	c.db = db
	return nil
}

// Close MySQL 연결 종료
func (c *Client) Close() error {
	c.mu.Lock()
	defer c.mu.Unlock()
	if c.db != nil {
		if err := c.db.Close(); err != nil {
			logger.Log.Error("Error while closing MySQL client", zap.Error(err))
			return err
		}
		c.db = nil
	}
	return nil
}

// withRetry 재시도 로직을 포함한 함수 실행
func withRetry(operation func() error) error {
	var lastErr error
	for i := 0; i < maxRetries; i++ {
		if err := operation(); err == nil {
			return nil
		} else {
			lastErr = err
			backoff := time.Duration(1<<uint(i)) * retryDelay
			if backoff > 1*time.Second {
				backoff = 1 * time.Second
			}
			time.Sleep(backoff)
		}
	}
	return fmt.Errorf("operation failed after %d retries, last error: %w", maxRetries, lastErr)
}

// checkAndReconnect 연결 상태 확인 및 재연결
func (c *Client) checkAndReconnect(ctx context.Context) error {
	c.mu.RLock()
	db := c.db
	c.mu.RUnlock()

	if db == nil {
		return c.connect()
	}

	// 커넥션 풀 상태 로깅
	stats := db.Stats()
	if stats.InUse >= stats.MaxOpenConnections-2 { // 거의 고갈 상태
		logger.Log.Warn("MySQL connection pool near exhaustion",
			zap.Int("inUse", stats.InUse),
			zap.Int("idle", stats.Idle),
			zap.Int("maxOpen", stats.MaxOpenConnections))
	}

	err := db.PingContext(ctx)
	if err == nil {
		return nil
	}

	logger.Log.Warn("MySQL connection lost, attempting to reconnect")
	return withRetry(func() error {
		return c.connect()
	})
}

// CustomTime은 JSON 마샬링을 위한 사용자 정의 시간 타입입니다
type CustomTime struct {
	time.Time
}

// MarshalJSON CustomTime을 JSON으로 마샬링할 때 문자열 형식으로 변환합니다
func (t CustomTime) MarshalJSON() ([]byte, error) {
	if t.IsZero() {
		return []byte("0"), nil
	}
	// 유닉스 타임스탬프가 아닌 문자열 형식으로 출력
	stamp := fmt.Sprintf("\"%s\"", t.Format("2006-01-02 15:04:05"))
	return []byte(stamp), nil
}

// ServerInfoFromDB 데이터베이스에서 가져온 서버 정보 구조체
type ServerInfoFromDB struct {
	WorldName            string     `json:"worldName"`
	ServerName           string     `json:"serverName"`
	WID                  string     `json:"wid"`
	CongestionThreshold  int        `json:"congestionThreshold"`
	SaturationThreshold  int        `json:"saturationThreshold"`
	CongestionLevel      int        `json:"congestionLevel"`
	MaxUserCount         int        `json:"maxUserCount"`
	MaintenanceStatus    bool       `json:"maintenanceStatus"`
	MaintenanceStartTime CustomTime `json:"maintenanceStartTime"`
	MaintenanceEndTime   CustomTime `json:"maintenanceEndTime"`
}

// CheckAccountExists PID가 존재하는지 확인하는 함수
func (c *Client) CheckAccountExists(ctx context.Context, pid string) (bool, error) {
	// 일관된 타임아웃 적용
	timeoutCtx, cancel := context.WithTimeout(ctx, mysqlOperationTimeout)
	defer cancel()

	if err := c.checkAndReconnect(timeoutCtx); err != nil {
		return false, fmt.Errorf("failed to check connection: %w", err)
	}

	c.mu.RLock()
	defer c.mu.RUnlock()

	tx, err := c.db.BeginTx(timeoutCtx, &sql.TxOptions{
		Isolation: sql.LevelReadCommitted,
		ReadOnly:  true,
	})
	if err != nil {
		return false, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	_, err = tx.ExecContext(timeoutCtx, "SET @returnCode = 0")
	if err != nil {
		return false, fmt.Errorf("failed to initialize returnCode variable: %w", err)
	}

	// spSelectAccountInfo 프로시저 호출
	rows, err := tx.QueryContext(timeoutCtx, "CALL spSelectAccountInfo(?, @returnCode)", pid)
	if err != nil {
		return false, fmt.Errorf("failed to call procedure: %w", err)
	}
	defer rows.Close()

	// 결과가 있는지 확인
	hasRows := false
	for rows.Next() {
		var accountPID string
		if err := rows.Scan(&accountPID); err != nil {
			logger.Log.Warn("Failed to scan account info row", zap.Error(err))
			continue
		}
		hasRows = true
		break
	}

	if err = rows.Err(); err != nil {
		return false, fmt.Errorf("error during row iteration: %w", err)
	}

	// 추가 결과셋 처리
	for rows.NextResultSet() {
		for rows.Next() {
		}
	}

	// 반환 코드 확인
	var returnCode sql.NullInt64
	if err = tx.QueryRowContext(timeoutCtx, "SELECT @returnCode").Scan(&returnCode); err != nil {
		return false, fmt.Errorf("failed to get return code: %w", err)
	}

	returnCodeValue := 0
	if returnCode.Valid {
		returnCodeValue = int(returnCode.Int64)
	} else {
		logger.Log.Debug("Return code is NULL, assuming failure")
		return false, nil
	}

	if err = tx.Commit(); err != nil {
		return false, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// 반환 코드가 0이면 성공, 그렇지 않으면 실패
	if returnCodeValue == 0 && hasRows {
		logger.Log.Debug("Account exists", zap.String("pid", pid))
		return true, nil
	}

	logger.Log.Debug("Account does not exist", zap.String("pid", pid), zap.Int("returnCode", returnCodeValue))
	return false, nil
}

func (c *Client) GetDBCurrentTime(ctx context.Context) (time.Time, error) {
	// 일관된 타임아웃 적용
	timeoutCtx, cancel := context.WithTimeout(ctx, mysqlOperationTimeout)
	defer cancel()

	if err := c.checkAndReconnect(timeoutCtx); err != nil {
		return time.Time{}, fmt.Errorf("failed to check connection: %w", err)
	}

	c.mu.RLock()
	defer c.mu.RUnlock()

	var dbTime time.Time
	err := c.db.QueryRowContext(timeoutCtx, "SELECT NOW()").Scan(&dbTime)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to get DB current time: %w", err)
	}

	return dbTime, nil
}

func (c *Client) GetServerInfosFromDB(ctx context.Context) (map[string]ServerInfoFromDB, error) {
	// 일관된 타임아웃 적용
	timeoutCtx, cancel := context.WithTimeout(ctx, mysqlOperationTimeout)
	defer cancel()

	if err := c.checkAndReconnect(timeoutCtx); err != nil {
		return nil, fmt.Errorf("failed to check connection: %w", err)
	}

	c.mu.RLock()
	defer c.mu.RUnlock()

	tx, err := c.db.BeginTx(timeoutCtx, &sql.TxOptions{
		Isolation: sql.LevelReadCommitted,
		ReadOnly:  true,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	_, err = tx.ExecContext(timeoutCtx, "SET @returnCode = 0")
	if err != nil {
		return nil, fmt.Errorf("failed to initialize returnCode variable: %w", err)
	}

	rows, err := tx.QueryContext(timeoutCtx, "CALL spSelectServerInfo(@returnCode)")
	if err != nil {
		return nil, fmt.Errorf("failed to call procedure: %w", err)
	}
	defer rows.Close()

	// 결과를 WID를 키로 하는 맵으로 저장
	serverInfoMap := make(map[string]ServerInfoFromDB)

	for rows.Next() {
		var info ServerInfoFromDB
		var maintenanceStatus int
		var maintenanceStartTime, maintenanceEndTime sql.NullTime

		if err := rows.Scan(
			&info.WorldName,
			&info.ServerName,
			&info.WID,
			&info.CongestionThreshold,
			&info.SaturationThreshold,
			&info.CongestionLevel,
			&info.MaxUserCount,
			&maintenanceStatus,
			&maintenanceStartTime,
			&maintenanceEndTime,
		); err != nil {
			logger.Log.Warn("Failed to scan server info row", zap.Error(err), zap.String("action", "skipping_row"))
			continue
		}

		info.MaintenanceStartTime = CustomTime{maintenanceStartTime.Time}
		info.MaintenanceEndTime = CustomTime{maintenanceEndTime.Time}
		info.MaintenanceStatus = maintenanceStatus != 0

		// WID를 키로 사용하여 맵에 저장
		serverInfoMap[info.WID] = info
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error during row iteration: %w", err)
	}

	for rows.NextResultSet() {
		for rows.Next() {
		}
	}

	var returnCode sql.NullInt64
	if err = tx.QueryRowContext(timeoutCtx, "SELECT @returnCode").Scan(&returnCode); err != nil {
		return nil, fmt.Errorf("failed to get return code: %w", err)
	}

	returnCodeValue := 0
	if returnCode.Valid {
		returnCodeValue = int(returnCode.Int64)
	} else {
		logger.Log.Debug("Return code is NULL, assuming success")
	}

	if returnCodeValue != 0 {
		return nil, fmt.Errorf("procedure returned error: returnCode=%d", returnCodeValue)
	}

	if err = tx.Commit(); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	if len(serverInfoMap) == 0 {
		logger.Log.Debug("No server info found in database")
		return make(map[string]ServerInfoFromDB), nil
	}
	return serverInfoMap, nil
}

// GetCharacterWorlds PlatformID로 캐릭터가 있는 월드 목록을 조회하는 함수
func (c *Client) GetCharacterWorlds(ctx context.Context, platformID string) ([]string, error) {
	// 일관된 타임아웃 적용
	timeoutCtx, cancel := context.WithTimeout(ctx, mysqlOperationTimeout)
	defer cancel()

	if err := c.checkAndReconnect(timeoutCtx); err != nil {
		return nil, fmt.Errorf("failed to check connection: %w", err)
	}

	c.mu.RLock()
	defer c.mu.RUnlock()

	tx, err := c.db.BeginTx(timeoutCtx, &sql.TxOptions{
		Isolation: sql.LevelReadCommitted,
		ReadOnly:  true,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	_, err = tx.ExecContext(timeoutCtx, "SET @returnCode = 0")
	if err != nil {
		return nil, fmt.Errorf("failed to initialize returnCode variable: %w", err)
	}

	// spSelectCharacterWorlds 프로시저 호출
	rows, err := tx.QueryContext(timeoutCtx, "CALL spSelectCharacterWorlds(?, @returnCode)", platformID)
	if err != nil {
		return nil, fmt.Errorf("failed to call spSelectCharacterWorlds procedure: %w", err)
	}
	defer rows.Close()

	var worldIDs []string
	for rows.Next() {
		var wid string
		if err := rows.Scan(&wid); err != nil {
			logger.Log.Warn("Failed to scan character world row",
				zap.Error(err),
				zap.String("platformID", platformID))
			continue
		}
		worldIDs = append(worldIDs, wid)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error during row iteration: %w", err)
	}

	// 추가 결과셋 처리
	for rows.NextResultSet() {
		for rows.Next() {
		}
	}

	// 반환 코드 확인
	var returnCode sql.NullInt64
	if err = tx.QueryRowContext(timeoutCtx, "SELECT @returnCode").Scan(&returnCode); err != nil {
		return nil, fmt.Errorf("failed to get return code: %w", err)
	}

	returnCodeValue := 0
	if returnCode.Valid {
		returnCodeValue = int(returnCode.Int64)
	} else {
		logger.Log.Debug("Return code is NULL, assuming success")
	}

	if returnCodeValue != 0 {
		return nil, fmt.Errorf("spSelectCharacterWorlds procedure returned error: returnCode=%d", returnCodeValue)
	}

	if err = tx.Commit(); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	logger.Log.Debug("Retrieved character worlds",
		zap.String("platformID", platformID),
		zap.Strings("worldIDs", worldIDs))

	return worldIDs, nil
}
