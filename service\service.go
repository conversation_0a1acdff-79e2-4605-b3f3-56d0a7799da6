package service

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net"
	"net/http"
	"os"
	"strconv"
	"sync"
	"time"

	"NSFrontServer/config"
	"NSFrontServer/logger"
	"NSFrontServer/mysqlutil"

	"slices"

	"github.com/hashicorp/consul/api"
	"go.uber.org/zap"
)

type ServerState int

const (
	Close ServerState = iota
	Open
)

type CurrentUser int

const (
	Good CurrentUser = iota
	Busy
	Full
)

type ServerInfo struct {
	Name         string      `json:"name"`
	IP           string      `json:"ip"`
	Port         int         `json:"port"`
	WID          string      `json:"wid"`
	CurrentUser  CurrentUser `json:"currentUser"`
	Concurrency  int         `json:"concurrency"`
	State        ServerState `json:"state"`
	API          int         `json:"api"`
	Days         int         `json:"days"`
	CreateAt     int64       `json:"createAt"`
	RegisterTime string      `json:"registerTime"`
	MainServer   string      `json:"mainServer"`
	Env          string      `json:"env"`
	Tags         []string    `json:"tags"`
}

type SimplifiedServerInfo struct {
	Name                 string      `json:"name"`
	WID                  string      `json:"wid"`
	CurrentUser          CurrentUser `json:"currentUser"`
	MaintenanceStatus    bool        `json:"maintenanceStatus"`
	MaintenanceStartTime string      `json:"maintenanceStartTime"`
	MaintenanceEndTime   string      `json:"maintenanceEndTime"`
}

type GateServer struct {
	IP   string `json:"ip"`
	Port string `json:"port"`
	Api  string `json:"api"`
}

// RedisGetter는 Redis 조회에 필요한 최소한의 인터페이스를 정의합니다
type RedisGetter interface {
	GetWorldStatus(wid string) (activeCount, maxCount int64, err error)
	GetMaxUserCount(wid string) int64
	SetMaxUserCount(wid string, maxCount int64) error
}

type Service struct {
	consul      *api.Client
	config      *config.Config
	appConfig   Config
	httpClient  *http.Client
	redisGetter RedisGetter
	mysqlClient *mysqlutil.Client

	servers      map[string][]ServerInfo
	serversMutex sync.RWMutex

	dbServers      map[string]mysqlutil.ServerInfoFromDB
	dbServersMutex sync.RWMutex

	// 캐시된 간소화 서버 정보 목록
	cachedServers      []SimplifiedServerInfo
	cachedServersMutex sync.RWMutex

	// DB 서버 시간 캐싱
	dbCurrentTime      time.Time
	dbCurrentTimeMutex sync.RWMutex

	maxRetries  int
	baseTimeout time.Duration
}

type Config struct {
	WhiteList   []string               `json:"whiteList"`
	CustomInfos []SimplifiedServerInfo `json:"infos"`
}

type LoginStatus int

const (
	LoginSuccess LoginStatus = iota
	LoginWaiting
	LoginInvalid
)

type LoginResult struct {
	Status        LoginStatus
	SteamID       string
	GateServer    *GateServer
	QueuePosition int64
}

func NewService(ctx context.Context, cfg *config.Config, redisGetter RedisGetter, mysqlClient *mysqlutil.Client) (*Service, int) {
	consulConfig := api.DefaultConfig()
	consulConfig.Address = fmt.Sprintf("%s:%d", cfg.Consul.Host, cfg.Consul.Port)

	client, err := api.NewClient(consulConfig)
	if err != nil {
		logger.Log.Error("ErrFailedToCreateConsulClient", zap.Error(err))
		return nil, config.ErrFailedToCreateConsulClient
	}

	appConfig := Config{
		WhiteList:   []string{"127.0.0.1", "::1"},
		CustomInfos: []SimplifiedServerInfo{},
	}

	httpClient := &http.Client{
		Timeout: 10 * time.Second,
	}

	service := &Service{
		consul:        client,
		config:        cfg,
		appConfig:     appConfig,
		httpClient:    httpClient,
		redisGetter:   redisGetter,
		mysqlClient:   mysqlClient,
		servers:       make(map[string][]ServerInfo),
		dbServers:     make(map[string]mysqlutil.ServerInfoFromDB),
		cachedServers: make([]SimplifiedServerInfo, 0),
		maxRetries:    3,
		baseTimeout:   5 * time.Second,
	}

	service.UpdateDBCurrentTime()

	return service, config.ErrSuccess
}

func (s *Service) Register(ctx context.Context, version string) int {
	hostname, err := os.Hostname()
	if err != nil {
		logger.Log.Error("Failed to get hostname for service registration",
			zap.Error(err),
			zap.Int("errorCode", config.ErrFailedToRegisterService),
			zap.String("errorMessage", config.ErrorMessages[config.ErrFailedToRegisterService]))
		return config.ErrFailedToRegisterService
	}

	ip, err := getOutboundIP()
	if err != nil {
		logger.Log.Error("Failed to get outbound IP for service registration",
			zap.Error(err),
			zap.Int("errorCode", config.ErrFailedToRegisterService),
			zap.String("errorMessage", config.ErrorMessages[config.ErrFailedToRegisterService]))
		return config.ErrFailedToRegisterService
	}

	ipv4 := ip.To4()
	addr := uint32(ipv4[0])<<24 | uint32(ipv4[1])<<16 | uint32(ipv4[2])<<8 | uint32(ipv4[3])

	id := fmt.Sprintf("FrontServer-%s-%d", s.config.Consul.WorldGroup, addr)

	refName := hostname
	if s.config.Service.PublicName != "" {
		refName = s.config.Service.PublicName
	}

	registration := &api.AgentServiceRegistration{
		ID:      id,
		Name:    "FrontServer",
		Tags:    []string{s.config.Consul.WorldGroup, refName},
		Address: ip.String(),
		Port:    s.config.Application.Port,
		Meta: map[string]string{
			"hostname": hostname,
			"version":  version,
			"env":      s.config.Service.Env,
		},
		Check: &api.AgentServiceCheck{
			HTTP:     fmt.Sprintf("http://%s:%d/heartbeat", ip, s.config.Application.Port),
			Interval: "15s",
			Timeout:  "3s",
		},
	}

	if err := s.consul.Agent().ServiceRegister(registration); err != nil {
		logger.Log.Error("Failed to register service with Consul",
			zap.Error(err),
			zap.String("serviceID", id),
			zap.String("serviceName", "FrontServer"),
			zap.Int("errorCode", config.ErrFailedToRegisterService),
			zap.String("errorMessage", config.ErrorMessages[config.ErrFailedToRegisterService]))
		return config.ErrFailedToRegisterService
	}

	logger.Log.Info("Successfully registered service with Consul",
		zap.String("serviceID", id),
		zap.String("serviceName", "FrontServer"),
		zap.String("address", ip.String()),
		zap.Int("port", s.config.Application.Port))

	return config.ErrSuccess
}

func (s *Service) GetServerInfosFromConsul(ctx context.Context) (map[string][]ServerInfo, int) {
	logger.Log.Info("GetServerInfosFromConsul started")
	discovery := s.config.Application.ServiceDiscovery
	logger.Log.Info("Consul discovery config",
		zap.String("serviceName", discovery.Name),
		zap.Strings("tags", discovery.Tags))

	var services []*api.ServiceEntry
	for i, tag := range discovery.Tags {
		logger.Log.Info("Processing Consul tag",
			zap.Int("tagIndex", i+1),
			zap.Int("totalTags", len(discovery.Tags)),
			zap.String("tag", tag))
		parts, _, err := s.consul.Health().Service(discovery.Name, tag, true, &api.QueryOptions{
			RequireConsistent: true,
		})

		if err != nil {
			logger.Log.Error("Failed to get services from Consul",
				zap.Error(err),
				zap.String("serviceName", discovery.Name),
				zap.String("tag", tag),
				zap.Int("errorCode", config.ErrFailedToGetServices),
				zap.String("errorMessage", config.ErrorMessages[config.ErrFailedToGetServices]))
			return nil, config.ErrFailedToGetServices
		}

		logger.Log.Info("Successfully got services from Consul for tag",
			zap.String("tag", tag),
			zap.Int("serviceCount", len(parts)))
		services = append(services, parts...)
	}

	if len(services) == 0 {
		logger.Log.Error("No services found in Consul",
			zap.Strings("tags", discovery.Tags),
			zap.String("serviceName", discovery.Name),
			zap.Int("errorCode", config.ErrFailedToGetServices),
			zap.String("errorMessage", config.ErrorMessages[config.ErrFailedToGetServices]))
		return nil, config.ErrFailedToGetServices
	}

	// 월드 ID별로 서버 정보를 그룹화
	newServerInfoMap := make(map[string][]ServerInfo)

	for _, service := range services {
		info, err := s.convertToServerInfo(service)
		if err != nil {
			logger.Log.Warn("Failed to convert service to ServerInfo",
				zap.Error(err),
				zap.String("serviceID", service.Service.ID))
			continue
		}

		// 해당 WID에 대한 서버 목록에 추가
		newServerInfoMap[info.WID] = append(newServerInfoMap[info.WID], info)
	}

	if len(newServerInfoMap) == 0 {
		logger.Log.Error("No valid server infos found after conversion",
			zap.Int("servicesCount", len(services)),
			zap.Int("errorCode", config.ErrFailedToGetServices),
			zap.String("errorMessage", config.ErrorMessages[config.ErrFailedToGetServices]))
		return nil, config.ErrFailedToGetServices
	}

	logger.Log.Info("Updating servers cache")
	s.serversMutex.Lock()
	s.servers = newServerInfoMap
	s.serversMutex.Unlock()

	logger.Log.Info("GetServerInfosFromConsul completed successfully",
		zap.Int("totalServices", len(services)),
		zap.Int("worldCount", len(newServerInfoMap)))
	return newServerInfoMap, config.ErrSuccess
}

func (s *Service) calculateCurrentUserFromDB(dbInfo mysqlutil.ServerInfoFromDB, wid string) CurrentUser {
	if dbInfo.CongestionLevel > 0 {
		adjustedLevel := dbInfo.CongestionLevel - 1

		if adjustedLevel >= int(Full) {
			return Full
		} else if adjustedLevel >= int(Busy) {
			return Busy
		} else {
			return Good
		}
	} else {
		activeCount, _, err := s.redisGetter.GetWorldStatus(wid)
		if err != nil {
			return Good
		}

		if activeCount >= int64(dbInfo.SaturationThreshold) && dbInfo.SaturationThreshold > 0 {
			return Full
		} else if activeCount >= int64(dbInfo.CongestionThreshold) && dbInfo.CongestionThreshold > 0 {
			return Busy
		} else {
			return Good
		}
	}
}

// convertToSimplifiedInfos는 서버 정보를 SimplifiedServerInfo로 변환
func (s *Service) convertToSimplifiedInfos(loadType string) []SimplifiedServerInfo {
	s.serversMutex.RLock()
	s.dbServersMutex.RLock()

	// 필터링된 서버 목록 생성
	var simplifiedInfos []SimplifiedServerInfo

	// 모든 서버 정보를 처리
	for wid, serverList := range s.servers {
		// 각 WID에 대한 서버 목록 처리
		for _, server := range serverList {
			// 필터링 조건 확인
			if s.shouldFilterServer(server) {
				continue
			}

			switch loadType {
			case "consul":
				// Consul 기반 정보만 사용
				simplifiedInfos = append(simplifiedInfos, SimplifiedServerInfo{
					Name:                 server.Name,
					WID:                  server.WID,
					CurrentUser:          s.calculateCurrentUser(server.WID),
					MaintenanceStatus:    false,
					MaintenanceStartTime: "0",
					MaintenanceEndTime:   "0",
				})

			case "db":
				// DB 정보가 있는 경우만 처리
				if dbInfo, exists := s.dbServers[wid]; exists {
					name := server.Name
					if dbInfo.WorldName != "" {
						name = dbInfo.WorldName
					}

					serverWid := server.WID
					if serverWid == "" {
						serverWid = dbInfo.WID
					}

					maintenanceStartTime := formatTimeToString(dbInfo.MaintenanceStartTime.Time)
					maintenanceEndTime := formatTimeToString(dbInfo.MaintenanceEndTime.Time)

					maintenanceStatus := false
					if isMaintenanceTime(maintenanceStartTime, maintenanceEndTime, s.GetDBCurrentTime()) {
						maintenanceStatus = true
					}

					dbInfoCopy := dbInfo
					currentUser := s.calculateCurrentUserFromDB(dbInfoCopy, serverWid)

					simplifiedInfos = append(simplifiedInfos, SimplifiedServerInfo{
						Name:                 name,
						WID:                  serverWid,
						CurrentUser:          currentUser,
						MaintenanceStatus:    maintenanceStatus,
						MaintenanceStartTime: maintenanceStartTime,
						MaintenanceEndTime:   maintenanceEndTime,
					})
				}

			default:
				// 기본: Consul과 DB 정보 모두 사용
				name := server.Name
				serverWid := server.WID
				var currentUser CurrentUser
				maintenanceStatus := false
				maintenanceStartTime := "0"
				maintenanceEndTime := "0"

				if dbInfo, exists := s.dbServers[wid]; exists {
					name = dbInfo.WorldName

					if serverWid == "" {
						serverWid = dbInfo.WID
					}

					maintenanceStartTime = formatTimeToString(dbInfo.MaintenanceStartTime.Time)
					maintenanceEndTime = formatTimeToString(dbInfo.MaintenanceEndTime.Time)

					if isMaintenanceTime(maintenanceStartTime, maintenanceEndTime, s.GetDBCurrentTime()) {
						maintenanceStatus = true
					}

					dbInfoCopy := dbInfo
					currentUser = s.calculateCurrentUserFromDB(dbInfoCopy, serverWid)
				} else {
					currentUser = s.calculateCurrentUser(serverWid)
				}

				simplifiedInfos = append(simplifiedInfos, SimplifiedServerInfo{
					Name:                 name,
					WID:                  serverWid,
					CurrentUser:          currentUser,
					MaintenanceStatus:    maintenanceStatus,
					MaintenanceStartTime: maintenanceStartTime,
					MaintenanceEndTime:   maintenanceEndTime,
				})
			}
		}
	}

	s.dbServersMutex.RUnlock()
	s.serversMutex.RUnlock()

	return simplifiedInfos
}

// SetCachedServerInfos는 Consul에서 서버 정보를 가져오고 캐시된 서버 정보를 설정
// UpdateDBCurrentTime DB 서버의 현재 시간을 업데이트
func (s *Service) UpdateDBCurrentTime() error {
	ctx, cancel := context.WithTimeout(context.Background(), s.baseTimeout)
	defer cancel()

	dbTime, err := s.mysqlClient.GetDBCurrentTime(ctx)
	if err != nil {
		logger.Log.Error("Failed to get DB current time", zap.Error(err))
		return fmt.Errorf("failed to get DB current time: %w", err)
	}

	s.dbCurrentTimeMutex.Lock()
	s.dbCurrentTime = dbTime
	s.dbCurrentTimeMutex.Unlock()

	return nil
}

func (s *Service) GetDBCurrentTime() time.Time {
	s.dbCurrentTimeMutex.RLock()
	defer s.dbCurrentTimeMutex.RUnlock()

	if s.dbCurrentTime.IsZero() {
		return time.Now()
	}

	return s.dbCurrentTime
}

func isMaintenanceTime(startTimeStr, endTimeStr string, dbTime time.Time) bool {
	if startTimeStr == "0" || endTimeStr == "0" || startTimeStr == "" || endTimeStr == "" {
		return false
	}

	// DB 시간의 타임존 사용
	dbLoc := dbTime.Location()

	startTime, startErr := time.ParseInLocation("2006-01-02 15:04:05", startTimeStr, dbLoc)
	endTime, endErr := time.ParseInLocation("2006-01-02 15:04:05", endTimeStr, dbLoc)

	if startErr != nil || endErr != nil {
		return false
	}

	return (dbTime.After(startTime) || dbTime.Equal(startTime)) &&
		(dbTime.Before(endTime) || dbTime.Equal(endTime))
}

func (s *Service) SetCachedServerInfos() (map[string][]ServerInfo, error) {
	logger.Log.Info("SetCachedServerInfos started")

	logger.Log.Info("Getting server infos from Consul")
	consulData, errCode := s.GetServerInfosFromConsul(context.Background())
	if errCode != config.ErrSuccess {
		err := fmt.Errorf("failed to get server infos from Consul: %d", errCode)
		logger.Log.Error("SetCachedServerInfos failed at Consul step",
			zap.Int("errorCode", errCode),
			zap.String("errorMessage", config.ErrorMessages[errCode]))
		return nil, err
	}
	logger.Log.Info("Successfully got server infos from Consul",
		zap.Int("worldCount", len(consulData)))

	loadType := s.config.Application.ServiceDiscovery.LoadType
	if loadType == "" {
		loadType = "all"
	}
	logger.Log.Info("Processing with loadType", zap.String("loadType", loadType))

	var dbServerInfoMap map[string]mysqlutil.ServerInfoFromDB

	if loadType == "db" || loadType == "all" {
		logger.Log.Info("Getting server infos from DB")
		ctx, cancel := context.WithTimeout(context.Background(), 2*s.baseTimeout)
		defer cancel()

		var err error
		dbServerInfoMap, err = s.mysqlClient.GetServerInfosFromDB(ctx)
		if err != nil {
			logger.Log.Error("Failed to get server infos from DB",
				zap.Error(err),
				zap.String("loadType", loadType))
			if loadType == "db" {
				return nil, fmt.Errorf("failed to get server infos from DB: %w", err)
			}
		} else {
			logger.Log.Info("Successfully got server infos from DB",
				zap.Int("serverCount", len(dbServerInfoMap)))
		}

		logger.Log.Info("Updating DB servers cache")
		s.dbServersMutex.Lock()
		s.dbServers = dbServerInfoMap
		s.dbServersMutex.Unlock()

		logger.Log.Info("Updating DB current time")
		s.UpdateDBCurrentTime()
	}

	logger.Log.Info("Converting to simplified server infos")
	simplifiedInfos := s.convertToSimplifiedInfos(loadType)
	logger.Log.Info("Converted to simplified server infos",
		zap.Int("simplifiedCount", len(simplifiedInfos)))

	logger.Log.Info("Updating cached servers")
	s.cachedServersMutex.Lock()
	s.cachedServers = simplifiedInfos
	s.cachedServersMutex.Unlock()

	logger.Log.Info("Getting max user counts for worlds")
	for _, info := range simplifiedInfos {
		s.GetMaxUserCount(info.WID)
	}

	logger.Log.Info("SetCachedServerInfos completed successfully",
		zap.Int("totalWorlds", len(consulData)),
		zap.Int("cachedServers", len(simplifiedInfos)))
	return consulData, nil
}

func formatTimeToString(t time.Time) string {
	if t.IsZero() {
		return "0"
	}
	return t.Format("2006-01-02 15:04:05")
}

func (s *Service) calculateCurrentUser(wid string) CurrentUser {
	activeCount, maxCount, err := s.redisGetter.GetWorldStatus(wid)
	if err != nil {
		return Good
	}

	if maxCount <= 0 {
		return Good
	}

	usage := float64(activeCount) / float64(maxCount) * 100
	switch {
	case usage >= 100:
		return Full
	case usage >= 40:
		return Busy
	default:
		return Good
	}
}

func (s *Service) shouldFilterServer(info ServerInfo) bool {
	tags := s.config.Application.ServiceDiscovery.Filter.Tags
	for _, tag := range tags {
		if slices.Contains(info.Tags, tag) {
			return true
		}
	}
	return false
}

func (s *Service) IsWorldUnderMaintenance(wid string) bool {
	s.cachedServersMutex.RLock()
	defer s.cachedServersMutex.RUnlock()

	for _, info := range s.cachedServers {
		if info.WID == wid {
			return info.MaintenanceStatus
		}
	}

	s.dbServersMutex.RLock()
	defer s.dbServersMutex.RUnlock()

	if _, exists := s.dbServers[wid]; exists {
		return true
	}

	return false
}

func (s *Service) convertToServerInfo(service *api.ServiceEntry) (ServerInfo, error) {
	domain := service.Service.Meta["public_domain"]
	gameport, err := strconv.Atoi(service.Service.Meta["public_port"])
	if err != nil {
		return ServerInfo{}, err
	}

	state := Close
	concurrency := 0
	for _, check := range service.Checks {
		if check.ServiceID != "" && check.Status == "passing" {
			state = Open
			concurrency = 1
			break
		}
	}

	registerTime := service.Service.Meta["register_time"]
	name := service.Service.Meta["name"]
	wid := service.Service.Meta["wid"]
	env := service.Service.Meta["env"]
	api, _ := strconv.Atoi(service.Service.Meta["api_port"])

	return ServerInfo{
		Name:         name,
		IP:           domain,
		Port:         gameport,
		WID:          wid,
		CurrentUser:  Good,
		Concurrency:  concurrency,
		State:        ServerState(state),
		Days:         0,
		CreateAt:     0,
		RegisterTime: registerTime,
		API:          api,
		Env:          env,
		Tags:         service.Service.Tags,
	}, nil
}

func (s *Service) GetConfig() Config {
	return s.appConfig
}

func (s *Service) GetCachedServerInfosFromCache() ([]SimplifiedServerInfo, error) {
	logger.Log.Debug("Getting cached simplified server infos")

	s.cachedServersMutex.RLock()
	defer s.cachedServersMutex.RUnlock()

	if len(s.cachedServers) == 0 {
		logger.Log.Debug("No cached simplified server infos found")
		return []SimplifiedServerInfo{}, nil
	}

	result := make([]SimplifiedServerInfo, len(s.cachedServers))
	copy(result, s.cachedServers)

	return result, nil
}

func (s *Service) GetMaxUserCount(wid string) int64 {
	s.dbServersMutex.RLock()
	defer s.dbServersMutex.RUnlock()

	if dbInfo, exists := s.dbServers[wid]; exists && dbInfo.MaxUserCount > 0 {
		if s.redisGetter != nil {
			maxUserCount := int64(dbInfo.MaxUserCount)
			err := s.redisGetter.SetMaxUserCount(wid, maxUserCount)
			if err != nil {
				logger.Log.Error("Failed to set max user count in Redis",
					zap.String("wid", wid),
					zap.Int64("maxUserCount", maxUserCount),
					zap.Error(err))
			}
			return maxUserCount
		}
		return int64(dbInfo.MaxUserCount)
	}
	if s.redisGetter != nil {
		return s.redisGetter.GetMaxUserCount(wid)
	}
	return 100 // 최종 기본값
}

func (s *Service) Close() error {
	return nil
}

func getOutboundIP() (net.IP, error) {
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	localAddr := conn.LocalAddr().(*net.UDPAddr)

	return localAddr.IP, nil
}

func (s *Service) AuthenticateSteamTicket(ctx context.Context, ticket string, appId string) (*SteamResponse, int) {
	profile := s.config.Application.Profile.SteamApps[appId]

	url := fmt.Sprintf("https://partner.steam-api.com/ISteamUserAuth/AuthenticateUserTicket/v1/?key=%s&appid=%s&ticket=%s",
		profile.Key, profile.AppID, ticket)

	req, err := http.NewRequestWithContext(ctx, "GET", url, nil)
	if err != nil {
		logger.Log.Error("Failed to create request for Steam API",
			zap.Error(err),
			zap.String("appId", appId),
			zap.Int("errorCode", config.ErrSteamAPIError),
			zap.String("errorMessage", config.ErrorMessages[config.ErrSteamAPIError]))
		return nil, config.ErrSteamAPIError
	}

	resp, err := s.httpClient.Do(req)
	if err != nil {
		logger.Log.Error("Failed to send request to Steam API",
			zap.Error(err),
			zap.String("appId", appId),
			zap.Int("errorCode", config.ErrSteamAPIError),
			zap.String("errorMessage", config.ErrorMessages[config.ErrSteamAPIError]))
		return nil, config.ErrSteamAPIError
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Log.Error("Failed to read response from Steam API",
			zap.Error(err),
			zap.String("appId", appId),
			zap.Int("errorCode", config.ErrSteamAPIError),
			zap.String("errorMessage", config.ErrorMessages[config.ErrSteamAPIError]))
		return nil, config.ErrSteamAPIError
	}

	var steamResponse SteamResponse
	if err := json.Unmarshal(body, &steamResponse); err != nil {
		logger.Log.Error("Failed to unmarshal response from Steam API",
			zap.Error(err),
			zap.String("appId", appId),
			zap.String("responseBody", string(body)),
			zap.Int("errorCode", config.ErrSteamAPIError),
			zap.String("errorMessage", config.ErrorMessages[config.ErrSteamAPIError]))
		return nil, config.ErrSteamAPIError
	}

	return &steamResponse, config.ErrSuccess
}

func (s *Service) AuthenticateKGToken(ctx context.Context, accessToken string, playerId string, appId string, platform string) (*KGResponse, int) {
	profile := s.config.Application.Profile.KGApps[appId]

	// KG.Url이 비어있으면 profile에서 url 사용
	url := s.config.Application.KG.Url
	if url == "" {
		url = profile.Url
	}
	url = url + "/service/v5/auth/validation"

	req, err := http.NewRequestWithContext(ctx, "POST", url, nil)
	if err != nil {
		logger.Log.Error("Failed to create request for KG API",
			zap.Error(err),
			zap.String("appId", appId),
			zap.String("playerId", playerId),
			zap.String("platform", platform),
			zap.Int("errorCode", config.ErrKGAPIError),
			zap.String("errorMessage", config.ErrorMessages[config.ErrKGAPIError]))
		return nil, config.ErrKGAPIError
	}

	req.Header.Add("Content-Type", "application/json;charset=UTF-8")
	req.Header.Add("appSecret", profile.AppSecret)
	req.Header.Add("Authorization", "KakaoAK "+profile.AdminKey)
	req.Header.Add("kgAppId", profile.AppId)
	req.Header.Add("platform", platform)
	req.Header.Add("accessToken", accessToken)
	req.Header.Add("playerId", playerId)

	resp, err := s.httpClient.Do(req)
	if err != nil {
		logger.Log.Error("Failed to send request to KG API",
			zap.Error(err),
			zap.String("appId", appId),
			zap.String("playerId", playerId),
			zap.String("platform", platform),
			zap.Int("errorCode", config.ErrKGAPIError),
			zap.String("errorMessage", config.ErrorMessages[config.ErrKGAPIError]))
		return nil, config.ErrKGAPIError
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Log.Error("Failed to read response from KG API",
			zap.Error(err),
			zap.String("appId", appId),
			zap.String("playerId", playerId),
			zap.Int("errorCode", config.ErrKGAPIError),
			zap.String("errorMessage", config.ErrorMessages[config.ErrKGAPIError]))
		return nil, config.ErrKGAPIError
	}

	var kgResponse KGResponse
	if err := json.Unmarshal(body, &kgResponse); err != nil {
		logger.Log.Error("Failed to unmarshal response from KG API",
			zap.Error(err),
			zap.String("appId", appId),
			zap.String("playerId", playerId),
			zap.String("responseBody", string(body)),
			zap.Int("errorCode", config.ErrKGAPIError),
			zap.String("errorMessage", config.ErrorMessages[config.ErrKGAPIError]))
		return nil, config.ErrKGAPIError
	}

	return &kgResponse, config.ErrSuccess
}

type SteamResponse struct {
	Response struct {
		Params struct {
			Result          string `json:"result"`
			SteamID         string `json:"steamid"`
			OwnerSteamID    string `json:"ownersteamid"`
			VACBanned       bool   `json:"vacbanned"`
			PublisherBanned bool   `json:"publisherbanned"`
		} `json:"params"`
		Error struct {
			ErrorCode int    `json:"errorcode"`
			ErrorDesc string `json:"errordesc"`
		} `json:"error"`
	} `json:"response"`
}

type KGResponse struct {
	Player struct {
		KGAppId string `json:"kgAppId"`
		PayerId string `json:"playerId"`
		Status  string `json:"status"`
	} `json:"player"`
	Desc string `json:"desc"`
}

func GenerateSessionToken() string {
	return fmt.Sprintf("%d", time.Now().UnixNano())
}

func MustAtoi(s string) int {
	i, err := strconv.Atoi(s)
	if err != nil {
		panic(err)
	}
	return i
}
