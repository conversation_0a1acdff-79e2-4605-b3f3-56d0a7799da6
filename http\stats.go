package http

import (
	"sync"
	"time"
)

type ResponseTimeStats struct {
	mu    sync.Mutex
	count int64
	sum   time.Duration
	min   time.Duration
	max   time.Duration
}

func (r *ResponseTimeStats) Add(duration time.Duration) {
	r.mu.Lock()
	defer r.mu.Unlock()
	if r.count == 0 || duration < r.min {
		r.min = duration
	}
	if duration > r.max {
		r.max = duration
	}
	r.sum += duration
	r.count++
}

func (r *ResponseTimeStats) Stats() (avg, min, max time.Duration) {
	r.mu.Lock()
	defer r.mu.Unlock()
	if r.count == 0 {
		return 0, 0, 0
	}
	return r.sum / time.Duration(r.count), r.min, r.max
}
