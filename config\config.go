package config

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/hashicorp/consul/api"
	"github.com/imdario/mergo"
)

var bootstrapLogFile *os.File = nil

func activeBootstrapLog(filename string) error {
	var err error
	bootstrapLogFile, err = os.OpenFile(filename, os.O_CREATE|os.O_WRONLY|os.O_TRUNC, 0644)
	return err
}

func Close() {
	if bootstrapLogFile != nil {
		bootstrapLogFile.Close()
	}
}

// 로그 메시지를 bootstrap_frontserver.txt 파일에 기록하는 함수
func logMessage(message string) error {
	if bootstrapLogFile == nil {
		return nil
	}

	timestamp := time.Now().Format("2006-01-02 15:04:05")
	logLine := fmt.Sprintf("[%s] %s\n", timestamp, message)

	_, err := bootstrapLogFile.WriteString(logLine)
	return err
}

// 설정 값을 JSON으로 변환하여 로그에 기록
func logConfigAsJSON(cfg *Config, source string) {
	logMessage(fmt.Sprintf("==== 로드된 설정 값 (%s) ====", source))

	// JSON으로 마샬링
	jsonBytes, err := json.MarshalIndent(cfg, "", "  ")
	if err != nil {
		logMessage(fmt.Sprintf("설정 값을 JSON으로 변환하는 데 실패했습니다: %v", err))
		return
	}

	// JSON 문자열을 여러 줄로 분할하여 각 줄을 로그에 기록
	jsonStr := string(jsonBytes)
	lines := strings.Split(jsonStr, "\n")
	for _, line := range lines {
		logMessage(line)
	}

	logMessage(fmt.Sprintf("==== 설정 값 로깅 완료 (%s) ====", source))
}

type Config struct {
	Service     ServiceConfig     `json:"Service"`
	Consul      ConsulConfig      `json:"Consul"`
	Log         LogConfig         `json:"Log"`
	Application ApplicationConfig `json:"FrontServer"`
	Common      CommonConfig      `json:"Common"`
}

type ServiceConfig struct {
	Env        string `json:"Environment"`
	Wid        int    `json:"WID"`
	PublicName string `json:"PublicName"`
}

type ConsulConfig struct {
	WorldGroup string `json:"worldgroup"`
	Host       string `json:"host"`
	Port       int    `json:"port"`
}

type LogConfig struct {
	LogSeverity    int    `json:"LogSeverity"`
	LogDir         string `json:"LogDir"`
	MaxLogFileSize int64  `json:"MaxLogFileSize"`
}

type ApplicationConfig struct {
	Port             int                    `json:"Port"`
	Log              string                 `json:"log"`
	Profile          ProfileConfig          `json:"profile"`
	Steam            SteamConfig            `json:"Steam"`
	KG               KGConfig               `json:"KG"`
	ServiceDiscovery ServiceDiscoveryConfig `json:"servicediscovery"`
}

type ProfileConfig struct {
	DefaultSteamAppId string                 `json:"DefaultSteamAppId"`
	SteamApps         map[string]SteamConfig `json:"SteamApps"`
	DefaultKGAppId    string                 `json:"DefaultKGAppId"`
	KGApps            map[string]KGConfig    `json:"KGApps"`
}

type CommonConfig struct {
	Redis    RedisConfig `json:"Redis"`
	CommonDB MySQLConfig `json:"CommonDB"`
}

type RedisConfig struct {
	Host          string `json:"host"`
	Port          int    `json:"port"`
	DB            int    `json:"DBIndex"`
	Password      string `json:"Password"`
	EnableCluster bool   `json:"ClusterEnable"`
	EnableTLS     bool   `json:"TLSEnable"`
	TLSPort       int    `json:"TLSPort"`
}

type MySQLConfig struct {
	Host      string `json:"Host"`
	Port      int    `json:"Port"`
	ID        string `json:"ID"`
	Password  string `json:"Password"`
	DBName    string `json:"DBName"`
	PoolCount int    `json:"PoolCount"`
}

type SteamConfig struct {
	Key   string `json:"key"`
	AppID string `json:"appId"`
}

type KGConfig struct {
	Url       string `json:"url"`
	AppSecret string `json:"appSecret"`
	AdminKey  string `json:"adminKey"`
	AppId     string `json:"appId"`
	Platform  string `json:"platform"`
}

type ServiceDiscoveryConfig struct {
	Name     string       `json:"name"`
	Tags     []string     `json:"tags"`
	Filter   FilterConfig `json:"filter"`
	LoadType string       `json:"loadType"` // "all", "consul", "db" 중 하나의 값을 가짐
}

type FilterConfig struct {
	Tags []string `json:"tags"`
}

func loadConfigFromFile(configPath string, cfg *Config) int {
	logMessage(fmt.Sprintf("설정 파일 로드 시도: %s", configPath))

	absConfigPath, err := filepath.Abs(configPath)
	if err != nil {
		logMessage(fmt.Sprintf("절대 경로 가져오기 실패 %s: %v", configPath, err))
		return ErrFailedToOpenConfig
	}

	file, err := os.Open(absConfigPath)
	if err != nil {
		logMessage(fmt.Sprintf("설정 파일 열기 실패 %s: %v", absConfigPath, err))
		return ErrFailedToOpenConfig
	}
	defer file.Close()

	decoder := json.NewDecoder(file)
	if err := decoder.Decode(cfg); err != nil {
		// 어떤 필드에서 오류가 발생했는지 식별 시도
		var syntaxError *json.SyntaxError
		var unmarshalTypeError *json.UnmarshalTypeError

		switch {
		case errors.As(err, &syntaxError):
			logMessage(fmt.Sprintf("구문 오류 발생 %s, 위치: %d: %v", absConfigPath, syntaxError.Offset, err))
		case errors.As(err, &unmarshalTypeError):
			logMessage(fmt.Sprintf("타입 오류 발생 %s, 필드: %s, 예상 타입: %v, 실제 값: %v",
				absConfigPath, unmarshalTypeError.Field, unmarshalTypeError.Type, unmarshalTypeError.Value))
		default:
			logMessage(fmt.Sprintf("설정 파일 디코딩 실패 %s: %v", absConfigPath, err))
		}

		return ErrFailedToDecodeConfig
	}

	logMessage(fmt.Sprintf("설정 파일 로드 성공: %s", absConfigPath))
	return ErrSuccess
}

func loadConfigFromBytes(data []byte, cfg *Config) int {
	logMessage("바이트 데이터에서 설정 로드 시도")

	reader := bytes.NewReader(data)
	decoder := json.NewDecoder(reader)
	if err := decoder.Decode(cfg); err != nil {
		var syntaxError *json.SyntaxError
		var unmarshalTypeError *json.UnmarshalTypeError

		switch {
		case errors.As(err, &syntaxError):
			logMessage(fmt.Sprintf("구문 오류 발생, 위치: %d: %v", syntaxError.Offset, err))
		case errors.As(err, &unmarshalTypeError):
			logMessage(fmt.Sprintf("타입 오류 발생, 필드: %s, 예상 타입: %v, 실제 값: %v",
				unmarshalTypeError.Field, unmarshalTypeError.Type, unmarshalTypeError.Value))
		default:
			logMessage(fmt.Sprintf("바이트 데이터 디코딩 실패: %v", err))
		}

		return ErrFailedToDecodeConfig
	}

	logMessage("바이트 데이터에서 설정 로드 성공")
	return ErrSuccess
}

func loadConfigFromConsul(address string, key string, wid int, cfg *Config) int {
	logMessage(fmt.Sprintf("Consul에서 설정 로드 시도 주소: %s, 키: %s, wid: %d", address, key, wid))

	consulConfig := api.DefaultConfig()
	consulConfig.Address = address

	// get config from consul
	client, err := api.NewClient(consulConfig)
	if err != nil {
		logMessage(fmt.Sprintf("Consul 클라이언트 생성 실패 %s: %v", address, err))
		return ErrFailedToDecodeConfig
	}

	kv := client.KV()
	pair, _, err := kv.Get(key, nil)
	if err != nil {
		logMessage(fmt.Sprintf("Consul에서 키 가져오기 실패 %s: %v", key, err))
		return ErrFailedToDecodeConfig
	}

	if pair == nil {
		logMessage(fmt.Sprintf("Consul에서 키를 찾을 수 없음 %s", key))
		return ErrFailedToDecodeConfig
	}

	// load config from bytes
	errCode := loadConfigFromBytes(pair.Value, cfg)
	if errCode != ErrSuccess {
		return errCode
	}

	// override config from world config
	var rawConfig map[string]json.RawMessage
	if err := json.Unmarshal(pair.Value, &rawConfig); err != nil {
		logMessage(fmt.Sprintf("Consul에서 받은 원시 설정 언마샬링 실패: %v", err))
		return ErrFailedToDecodeConfig
	}

	if rawWorldConfig, ok := rawConfig["World_Config"]; ok {
		var worldConfig map[string]json.RawMessage
		if err := json.Unmarshal(rawWorldConfig, &worldConfig); err != nil {
			logMessage(fmt.Sprintf("World_Config 언마샬링 실패: %v", err))
			return ErrFailedToDecodeConfig
		}

		widStr := strconv.Itoa(wid)
		if rawServiceConfig, ok := worldConfig[widStr]; ok {
			if err := json.Unmarshal(rawServiceConfig, cfg); err != nil {
				logMessage(fmt.Sprintf("WID %d에 대한 월드 특정 설정 언마샬링 실패: %v", wid, err))
				return ErrFailedToDecodeConfig
			}
		} else {
			logMessage(fmt.Sprintf("WID %d에 대한 월드 특정 설정을 찾을 수 없음", wid))
		}
	} else {
		logMessage("Consul 데이터에서 World_Config 섹션을 찾을 수 없음")
	}
	return ErrSuccess
}

func Load(bootstrap string) (*Config, int) {
	if bootstrap != "" {
		activeBootstrapLog(bootstrap)
	}

	logMessage("설정 로딩 프로세스 시작")
	cfg := &Config{}

	// load service config
	{
		logMessage("서비스 설정 로드 중")
		err := loadConfigFromFile("../conf.d/service.json", cfg)
		if err != ErrSuccess {
			logMessage(fmt.Sprintf("서비스 설정 로드 실패, 오류 코드: %d", err))
			return nil, err
		}
	}

	// load system config
	{
		logMessage("시스템 설정 로드 중")
		err := loadConfigFromFile("../conf.d/system.json", cfg)
		if err != ErrSuccess {
			logMessage(fmt.Sprintf("시스템 설정 로드 실패, 오류 코드: %d", err))
			return nil, err
		}
	}

	// load consul config
	{
		logMessage("Consul에서 설정 로드 중")
		address := fmt.Sprintf("%s:%d", cfg.Consul.Host, cfg.Consul.Port)
		key := fmt.Sprintf("%s/config", cfg.Consul.WorldGroup)
		err := loadConfigFromConsul(address, key, cfg.Service.Wid, cfg)
		if err != ErrSuccess {
			logMessage(fmt.Sprintf("Consul에서 설정 로드 실패, 오류 코드: %d", err))
			return nil, err
		}
	}

	// override 파일이 존재하는 경우 처리
	logMessage("오버라이드 설정 파일 확인 중")
	if _, err := os.Stat("NSFrontServer.override.json"); err == nil {
		logMessage("오버라이드 파일 발견, 오버라이드 적용")
		overrideFile, err := os.Open("NSFrontServer.override.json")
		if err != nil {
			logMessage(fmt.Sprintf("오버라이드 파일 열기 실패: %v", err))
			return nil, ErrFailedToOpenConfig
		}
		defer overrideFile.Close()

		// 오버라이드 파일 내용 로깅
		fileBytes, err := io.ReadAll(overrideFile)
		if err != nil {
			logMessage(fmt.Sprintf("오버라이드 파일 읽기 실패: %v", err))
			return nil, ErrFailedToOpenConfig
		}

		var rawData map[string]any
		if err := json.Unmarshal(fileBytes, &rawData); err != nil {
			logMessage(fmt.Sprintf("오버라이드 JSON 언마샬링 실패: %v", err))
			return nil, ErrFailedToOpenConfig
		}

		cfgOverride := &Config{}
		if err := json.Unmarshal(fileBytes, cfgOverride); err != nil {
			logMessage(fmt.Sprintf("오버라이드 설정 언마샬링 실패: %v", err))
			return nil, ErrFailedToOpenConfig
		}

		if err := mergo.Merge(cfg, cfgOverride, mergo.WithOverride); err != nil {
			logMessage(fmt.Sprintf("오버라이드 설정 병합 실패: %v", err))
			return nil, ErrFailedToOpenConfig
		}

		// Common 설정이 있는 경우 처리
		if commonConfig, exists := rawData["Common"].(map[string]any); exists {
			// Redis 설정이 있는 경우에만 처리
			if redisConfig, exists := commonConfig["Redis"].(map[string]any); exists {

				// ClusterEnable 필드가 JSON에 있는 경우에만 적용
				if _, hasCluster := redisConfig["ClusterEnable"]; hasCluster {
					cfg.Common.Redis.EnableCluster = cfgOverride.Common.Redis.EnableCluster
				}
				// TLSEnable 필드가 JSON에 있는 경우에만 적용
				if _, hasTLS := redisConfig["TLSEnable"]; hasTLS {
					cfg.Common.Redis.EnableTLS = cfgOverride.Common.Redis.EnableTLS
				}
			}
		}
	} else {
		logMessage("오버라이드 파일이 없음, 기본 설정으로 계속 진행")
	}

	// Profile 설정이 있는 경우 처리
	{
		if len(cfg.Application.Profile.KGApps) == 0 && cfg.Application.KG.AppId != "" {
			cfg.Application.Profile.KGApps = make(map[string]KGConfig)
			cfg.Application.Profile.KGApps[cfg.Application.KG.AppId] = KGConfig{
				Url:       cfg.Application.KG.Url,
				AppSecret: cfg.Application.KG.AppSecret,
				AdminKey:  cfg.Application.KG.AdminKey,
				AppId:     cfg.Application.KG.AppId,
				Platform:  cfg.Application.KG.Platform,
			}

			cfg.Application.Profile.DefaultKGAppId = cfg.Application.KG.AppId
		}

		if len(cfg.Application.Profile.SteamApps) == 0 && cfg.Application.Steam.AppID != "" {
			cfg.Application.Profile.SteamApps = make(map[string]SteamConfig)
			cfg.Application.Profile.SteamApps[cfg.Application.Steam.AppID] = SteamConfig{
				Key:   cfg.Application.Steam.Key,
				AppID: cfg.Application.Steam.AppID,
			}

			cfg.Application.Profile.DefaultSteamAppId = cfg.Application.Steam.AppID
		}
	}

	// 환경 변수 적용
	if envPort := os.Getenv("PORT"); envPort != "" {
		logMessage(fmt.Sprintf("PORT 환경 변수 발견: %s", envPort))
		port, err := strconv.Atoi(envPort)
		if err != nil {
			logMessage(fmt.Sprintf("잘못된 PORT 환경 변수: %v", err))
			return nil, ErrInvalidPortEnv
		}
		cfg.Application.Port = port
	}

	if envEnv := os.Getenv("NODE_ENV"); envEnv != "" {
		logMessage(fmt.Sprintf("NODE_ENV 환경 변수 발견: %s", envEnv))
		cfg.Service.Env = envEnv
	}

	logConfigAsJSON(cfg, "최종 설정")
	logMessage("설정 로딩 성공적으로 완료")
	return cfg, ErrSuccess
}
