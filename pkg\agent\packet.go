package agent

import "encoding/binary"

type Packet struct {
	PacketType string
	*PacketBuffer
}

const (
	PacketTypeOffset = 4
)

func CreateSendPacket(packetType string) *Packet {
	buf := NewPacketBuffer()

	buf.WriteUint32(0)
	buf.WriteString(packetType)
	return &Packet{
		PacketBuffer: buf,
		PacketType:   packetType,
	}
}

func CreateRecvPacket(data []byte) (*Packet, error) {
	buf := NewPacketBuffer()

	buf.WriteUint32(uint32(len(data)))
	buf.buf.Write(data)

	packetType, err := buf.ReadString(PacketTypeOffset)
	if err != nil {
		return nil, err
	}

	return &Packet{
		PacketBuffer: buf,
		PacketType:   packetType,
	}, nil
}

func (p *Packet) SetHeader() []byte {
	size := uint32(p.buf.Len())
	binary.LittleEndian.PutUint32(p.buf.Bytes()[0:PacketTypeOffset], size)

	return p.buf.Bytes()
}

func (p *Packet) GetPayloadOffset() int {
	return PacketTypeOffset + 8 + len(p.PacketType)
}
