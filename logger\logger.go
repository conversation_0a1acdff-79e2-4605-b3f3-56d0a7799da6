package logger

import (
	"fmt"
	"net"
	"os"
	"path/filepath"
	"time"

	"NSFrontServer/config"

	"go.uber.org/zap"
	"go.uber.org/zap/buffer"
	"go.uber.org/zap/zapcore"
)

var Log *zap.Logger

// FormatEncoder는 로그 메시지를 커스텀 형식으로 인코딩
type FormatEncoder struct {
	zapcore.Encoder
	hostIP string
	tzName string
}

// zap라이브러리에서 호출할수도 있어서 구현
func (e *FormatEncoder) Clone() zapcore.Encoder {
	return &FormatEncoder{
		Encoder: e.Encoder.Clone(),
		hostIP:  e.hostIP,
		tzName:  e.tzName,
	}
}

// EncodeEntry는 로그 항목을 커스텀 형식으로 인코딩
func (e *FormatEncoder) EncodeEntry(entry zapcore.Entry, fields []zapcore.Field) (*buffer.Buffer, error) {
	// 원래 메시지 저장
	originalMessage := entry.Message

	// 커스텀 형식으로 메시지 변경
	customMessage := fmt.Sprintf("[%s] [%s] [%s] [FrontServer] [%s] [%s]",
		entry.Time.Format("2006/01/02 15:04:05.000"),
		e.tzName,
		e.hostIP,
		entry.Level.String(),
		originalMessage)

	// 메시지 변경
	entry.Message = customMessage

	return e.Encoder.EncodeEntry(entry, fields)
}

func Initialize(cfg *config.Config, debug bool) int {
	absLogPath, err := filepath.Abs(filepath.Join(cfg.Log.LogDir, "frontserver"))
	if err != nil {
		return config.ErrFailedToOpenConfig
	}

	if err := os.MkdirAll(absLogPath, os.ModePerm); err != nil {
		return config.ErrFailedToCreateLogDir
	}

	if cfg.Log.MaxLogFileSize == 0 {
		cfg.Log.MaxLogFileSize = 100 * 1024 * 1024 // 100MB
	}

	appLogger := &RotatingFileWriter{
		basePath: absLogPath,
		baseName: "app",
		maxSize:  cfg.Log.MaxLogFileSize,
	}

	errorLogger := &RotatingFileWriter{
		basePath: absLogPath,
		baseName: "error",
		maxSize:  cfg.Log.MaxLogFileSize,
	}

	appLogger.Rotate(0)
	errorLogger.Rotate(0)

	// 호스트 IP 가져오기
	hostIP := "127.0.0.1"
	conn, err := net.Dial("udp", "*******:80")
	if err == nil {
		localAddr := conn.LocalAddr().(*net.UDPAddr)
		hostIP = localAddr.IP.String()
		conn.Close()
	}

	// 타임존 약어 가져오기
	tzName, _ := time.Now().Zone()

	// 커스텀 인코더 설정
	encoderConfig := zapcore.EncoderConfig{
		TimeKey:        "",
		LevelKey:       "",
		NameKey:        "",
		CallerKey:      "",
		MessageKey:     "msg",
		StacktraceKey:  "stacktrace",
		LineEnding:     zapcore.DefaultLineEnding,
		EncodeLevel:    zapcore.LowercaseLevelEncoder,
		EncodeTime:     zapcore.EpochTimeEncoder,
		EncodeDuration: zapcore.SecondsDurationEncoder,
		EncodeCaller:   zapcore.ShortCallerEncoder,
		EncodeName:     zapcore.FullNameEncoder,
	}

	// 기본 인코더 생성
	baseEncoder := zapcore.NewConsoleEncoder(encoderConfig)

	// 커스텀 로그 형식 생성
	encoder := &FormatEncoder{
		Encoder: baseEncoder,
		hostIP:  hostIP,
		tzName:  tzName,
	}

	// 에러 로그는 에러 로그 파일에만, 일반 로그는 일반 로그 파일에만 기록되도록 설정
	infoLevel := zap.LevelEnablerFunc(func(lvl zapcore.Level) bool {
		return lvl >= zapcore.InfoLevel && lvl < zapcore.ErrorLevel
	})

	errorLevel := zap.LevelEnablerFunc(func(lvl zapcore.Level) bool {
		return lvl >= zapcore.ErrorLevel
	})

	debugLevel := zap.LevelEnablerFunc(func(lvl zapcore.Level) bool {
		return lvl >= zapcore.DebugLevel
	})

	core := zapcore.NewTee(
		zapcore.NewCore(encoder, zapcore.AddSync(appLogger), infoLevel),
		zapcore.NewCore(encoder, zapcore.AddSync(errorLogger), errorLevel),
		zapcore.NewCore(encoder, zapcore.AddSync(os.Stdout), debugLevel),
	)

	logger := zap.New(core, zap.AddCaller(), zap.AddCallerSkip(1))

	Log = logger
	zap.ReplaceGlobals(Log)

	go func() {
		now := time.Now()
		nextMidnight := time.Date(
			now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location(),
		)

		time.Sleep(time.Until(nextMidnight))

		for {
			<-time.After(time.Hour * 24)

			appLogger.Rotate(0)
			errorLogger.Rotate(0)
		}
	}()

	return config.ErrSuccess
}

func Sync() int {
	if err := Log.Sync(); err != nil {
		return config.ErrFailedToSyncLogger
	}
	return config.ErrSuccess
}
