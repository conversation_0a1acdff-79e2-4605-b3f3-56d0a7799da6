package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"os/signal"
	"path/filepath"
	"runtime/debug"
	"syscall"
	"time"

	cfg "NSFrontServer/config"
	httpserver "NSFrontServer/http"
	"NSFrontServer/logger"
	"NSFrontServer/mysqlutil"
	"NSFrontServer/pkg/agent"
	"NSFrontServer/redisutil"
	"NSFrontServer/service"

	"go.uber.org/zap"
)

var (
	major    string
	minor    string
	revision string
)

func main() {
	exePath, err := os.Executable()
	if err == nil {
		exeDir := filepath.Dir(exePath)
		_ = os.Chdir(exeDir)
	}

	version := flag.Bool("version", false, "")
	bootstrap := flag.String("bootstrap", "", "ex. -bootstrap=bootstrap_frontserver.txt")
	process_class_name := flag.String("process_class_name", "FrontServer", "ex. -process_class_name=FrontServer")
	flag.Parse()

	if version != nil && *version {
		fmt.Printf("Version: %s.%s.%s\n", major, minor, revision)
		return
	}

	if err := os.MkdirAll("logs/frontserver", 0755); err != nil {
		fmt.Printf("Failed to create log directory: %v\n", err)
	}

	defer func() {
		if r := recover(); r != nil {
			logger.Log.Error("Panic recovered", zap.Any("reason", r))
			stackTrace := debug.Stack()
			err := os.WriteFile("logs/frontserver/crash.txt", stackTrace, 0644)
			if err != nil {
				logger.Log.Error("Failed to write stack trace to crash.txt", zap.Error(err))
				fmt.Println("Failed to write stack trace to crash.txt:", err)
			} else {
				fmt.Println("Stack trace written to crash.txt")
			}
		}
	}()

	errCode, errorLocation := run(*bootstrap, *process_class_name)
	if errCode != cfg.ErrSuccess {
		stackTrace := debug.Stack()
		f, err := os.OpenFile("logs/frontserver/run_error.log", os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
		if err == nil {
			timestamp := time.Now().Format("2006-01-02 15:04:05")
			errorMessage := cfg.ErrorMessages[errCode]
			f.WriteString(fmt.Sprintf("\n[%s] Application failed with error code: %d (%s) at %s\n",
				timestamp, errCode, errorMessage, errorLocation))
			f.Write(stackTrace)
			f.Close()
		}
		logger.Log.Error("Application failed",
			zap.Int("errorCode", errCode),
			zap.String("errorMessage", cfg.ErrorMessages[errCode]),
			zap.String("location", errorLocation))
		return
	}
}

func run(bootstrap string, classname string) (int, string) {
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	config, errCode := cfg.Load(bootstrap)
	if errCode != cfg.ErrSuccess {
		return errCode, "config.Load"
	}
	defer cfg.Close()

	if errCode := logger.Initialize(config, false); errCode != cfg.ErrSuccess {
		return errCode, "logger.Initialize"
	}

	agent := agent.NewClient(classname, *config, logger.Log, func() {
		logger.Log.Info("Application terminated")

		cancel()
	})
	agent.ChangeStatus("BOOT_UP_PROGRESS")

	redisClient, errCode := redisutil.NewClient(config)
	if errCode != cfg.ErrSuccess {
		return errCode, "redisutil.NewClient"
	}
	defer redisClient.Close()

	// MySQL 클라이언트 초기화
	mysqlClient, errCode := mysqlutil.NewClient(config)
	if errCode != cfg.ErrSuccess {
		return errCode, "mysqlutil.NewClient"
	}
	defer mysqlClient.Close()

	svc, errCode := service.NewService(ctx, config, redisClient, mysqlClient)
	if errCode != cfg.ErrSuccess {
		return errCode, "service.NewService"
	}
	defer svc.Close()

	version := fmt.Sprintf("%s.%s.%s", major, minor, revision)
	if errCode := svc.Register(ctx, version); errCode != cfg.ErrSuccess {
		return errCode, "service.Register"
	}

	httpServer := httpserver.NewServer(config, svc, redisClient, mysqlClient)
	if errCode := httpServer.Initialize(ctx); errCode != cfg.ErrSuccess {
		return errCode, "httpserver.Initialize"
	}

	if errCode := httpServer.Start(ctx); errCode != cfg.ErrSuccess {
		return errCode, "httpserver.Start"
	}

	logger.Log.Info("Application started",
		zap.String("env", config.Service.Env),
		zap.Int("port", config.Application.Port))

	go runPeriodicSync(ctx, svc, redisClient)
	go runPeriodicLocalAgentSync(ctx, agent, 5*time.Second, httpServer)

	errCode, location := waitForShutdown(ctx, cancel)
	return errCode, location
}

func runPeriodicSync(ctx context.Context, svc *service.Service, redisClient *redisutil.Client) {
	logger.Log.Info("Starting periodic sync routine")
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	tickCount := 0
	for {
		select {
		case <-ctx.Done():
			logger.Log.Info("Stopping periodic sync routine", zap.Int("totalTicks", tickCount))
			return
		case <-ticker.C:
			tickCount++
			logger.Log.Info("Periodic sync tick triggered", zap.Int("tickNumber", tickCount))

			startTime := time.Now()
			if errCode := syncState(ctx, svc, redisClient); errCode != cfg.ErrSuccess {
				logger.Log.Error("Error in syncState",
					zap.Int("errorCode", errCode),
					zap.String("errorMessage", cfg.ErrorMessages[errCode]),
					zap.Duration("duration", time.Since(startTime)))
			} else {
				logger.Log.Info("Periodic sync completed successfully",
					zap.Int("tickNumber", tickCount),
					zap.Duration("duration", time.Since(startTime)))
			}
		}
	}
}

func runPeriodicLocalAgentSync(ctx context.Context, agent *agent.Client, interval time.Duration, httpServer *httpserver.Server) {
	logger.Log.Info("Starting periodic LocalAgent sync routine")
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			agent.ChangeStatus("SHUT_DOWN_PROGRESS")

			logger.Log.Info("Stopping periodic LocalAgent sync routine")

			agent.Close()
			return
		case <-ticker.C:
			if agent.IsConnected() {
				_, _, max := httpServer.GetStats()
				agent.Update(max)
			} else {
				const host = "127.0.0.1:4304"
				err := agent.Connect(host, 2*time.Second)
				if err != nil {
					logger.Log.Warn(fmt.Sprintf("Failed to connect to LocalAgent(%s)", host), zap.Error(err))
				}

				logger.Log.Info(fmt.Sprintf("Connected to LocalAgent(%s)", host))

				err = agent.SendHello()
				if err != nil {
					logger.Log.Warn("Failed to send hello to LocalAgent", zap.Error(err))
					continue
				}

				agent.SendMessageToLocalAgent("Hello from FrontServer")
				agent.ChangeStatus("ON_SERVICE")
			}
		}
	}
}

func syncState(ctx context.Context, svc *service.Service, redisClient *redisutil.Client) int {
	logger.Log.Info("Starting syncState execution")

	logger.Log.Info("Calling SetCachedServerInfos")
	serverInfoMap, err := svc.SetCachedServerInfos()
	if err != nil {
		logger.Log.Error("Failed to set cached server infos",
			zap.Error(err),
			zap.Int("errorCode", cfg.ErrFailedToGetServices),
			zap.String("errorMessage", cfg.ErrorMessages[cfg.ErrFailedToGetServices]))
		return cfg.ErrFailedToGetServices
	}
	logger.Log.Info("SetCachedServerInfos completed successfully",
		zap.Int("worldCount", len(serverInfoMap)))

	logger.Log.Info("Starting Redis sync")
	errCode := redisClient.SyncWithRedis(ctx, serverInfoMap)
	if errCode != cfg.ErrSuccess {
		logger.Log.Error("Failed to sync with Redis",
			zap.Int("errorCode", errCode),
			zap.String("errorMessage", cfg.ErrorMessages[errCode]))
	} else {
		logger.Log.Info("Redis sync completed successfully")
	}

	logger.Log.Info("syncState execution completed", zap.Int("resultCode", errCode))
	return errCode
}

func waitForShutdown(ctx context.Context, cancel context.CancelFunc) (int, string) {
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	select {
	case <-sigChan:
		logger.Log.Info("Received shutdown signal, gracefully shutting down...")
		cancel()
		time.Sleep(time.Second)
	case <-ctx.Done():
		logger.Log.Info("Context cancelled, shutting down...")
	}

	return cfg.ErrSuccess, "normal shutdown"
}
