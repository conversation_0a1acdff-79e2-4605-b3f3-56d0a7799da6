{"level":"info","ts":"2024-12-09T12:56:42.997+0900","caller":"NSFrontServer/main.go:78","msg":"Initializing HTTP server"}
{"level":"info","ts":"2024-12-09T12:56:42.998+0900","caller":"NSFrontServer/main.go:45","msg":"Application started","env":"development","port":3000}
{"level":"info","ts":"2024-12-09T12:56:42.998+0900","caller":"runtime/asm_amd64.s:1695","msg":"Starting periodic sync routine"}
{"level":"error","ts":"2024-12-09T12:56:42.998+0900","caller":"runtime/asm_amd64.s:1695","msg":"HTTP server start failed","error":"listen tcp4 :3000: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.","stacktrace":"runtime.goexit\n\tC:/Program Files/Go/src/runtime/asm_amd64.s:1695"}
{"level":"info","ts":"2024-12-09T12:56:51.516+0900","caller":"NSFrontServer/main.go:95","msg":"Received shutdown signal, gracefully shutting down..."}
{"level":"info","ts":"2024-12-09T12:56:51.516+0900","caller":"runtime/asm_amd64.s:1695","msg":"Stopping periodic sync routine"}
{"level":"info","ts":"2024-12-09T12:58:06.568+0900","caller":"NSFrontServer/main.go:78","msg":"Initializing HTTP server"}
{"level":"info","ts":"2024-12-09T12:58:06.568+0900","caller":"NSFrontServer/main.go:45","msg":"Application started","env":"development","port":3000}
{"level":"info","ts":"2024-12-09T12:58:06.568+0900","caller":"runtime/asm_amd64.s:1695","msg":"Starting periodic sync routine"}
{"level":"error","ts":"2024-12-09T12:58:06.568+0900","caller":"runtime/asm_amd64.s:1695","msg":"HTTP server start failed","error":"listen tcp4 :3000: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.","stacktrace":"runtime.goexit\n\tC:/Program Files/Go/src/runtime/asm_amd64.s:1695"}
{"level":"info","ts":"2024-12-09T12:58:13.458+0900","caller":"NSFrontServer/main.go:95","msg":"Received shutdown signal, gracefully shutting down..."}
{"level":"info","ts":"2024-12-09T12:58:13.458+0900","caller":"runtime/asm_amd64.s:1695","msg":"Stopping periodic sync routine"}
{"level":"info","ts":"2024-12-09T15:04:45.140+0900","caller":"NSFrontServer/main.go:78","msg":"Initializing HTTP server"}
{"level":"info","ts":"2024-12-09T15:04:45.141+0900","caller":"NSFrontServer/main.go:45","msg":"Application started","env":"development","port":3000}
{"level":"info","ts":"2024-12-09T15:04:45.142+0900","caller":"runtime/asm_amd64.s:1695","msg":"Starting periodic sync routine"}
{"level":"info","ts":"2024-12-09T15:04:53.387+0900","caller":"NSFrontServer/main.go:95","msg":"Received shutdown signal, gracefully shutting down..."}
{"level":"info","ts":"2024-12-09T15:04:53.387+0900","caller":"runtime/asm_amd64.s:1695","msg":"Stopping periodic sync routine"}
