package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"time"
)

type RotatingFileWriter struct {
	basePath    string
	baseName    string
	maxSize     int64
	current     *os.File
	currentIdx  int
	currentSize int64
}

func (w *RotatingFileWriter) Write(p []byte) (n int, err error) {
	if w.currentSize+int64(len(p)) > w.maxSize {
		w.Rotate(w.currentIdx + 1)
	}
	n, err = w.current.Write(p)
	w.currentSize += int64(n)
	return n, err
}

func (w *RotatingFileWriter) Rotate(index int) {
	if w.current != nil {
		w.current.Close()
	}

	currentDate := time.Now().Format("2006-01-02")
	newFileName := fmt.Sprintf("%s_%s.%d.log", w.baseName, currentDate, index)
	newFilePath := filepath.Join(w.basePath, newFileName)

	file, err := os.OpenFile(newFilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		panic(fmt.Sprintf("failed to create log file: %v", err))
	}

	w.current = file
	w.currentSize = 0
	w.currentIdx = index
}
