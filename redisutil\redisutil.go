package redisutil

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"sync"
	"time"

	"NSFrontServer/config"
	"NSFrontServer/logger"
	"NSFrontServer/service"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
)

const (
	defaultMaxSessions       = 5000
	sessionExpirationTime    = 720 * time.Minute
	sessionWaitingExpireTime = 5 * time.Minute
	redisConnectTimeout      = 5 * time.Second
	redisOperationTimeout    = 30 * time.Second
	lockTimeout              = 30 * time.Second
	maxRetries               = 3
	retryDelay               = 100 * time.Millisecond

	// Redis 연결 관련 상수
	poolSize     = 50
	minIdleConns = 10
	readTimeout  = 5 * time.Second
	writeTimeout = 5 * time.Second
	poolTimeout  = 5 * time.Second
	idleTimeout  = 240 * time.Second
)

var unlockScript = `
if redis.call("GET", KEYS[1]) == ARGV[1] then
    return redis.call("DEL", KEYS[1])
else
    return 0
end
`

type Client struct {
	client redis.UniversalClient
	cfg    *config.Config
	mu     sync.RWMutex
}

var (
	httpClientOnce     sync.Once
	httpClientInstance *http.Client
)

func getHTTPClient() *http.Client {
	httpClientOnce.Do(func() {
		httpClientInstance = &http.Client{
			Timeout: 5 * time.Second,
			Transport: &http.Transport{
				MaxIdleConns:        100,
				IdleConnTimeout:     90 * time.Second,
				DisableCompression:  true,
				MaxConnsPerHost:     10,
				DisableKeepAlives:   false,
				ForceAttemptHTTP2:   true,
				MaxIdleConnsPerHost: 10,
				DialContext: (&net.Dialer{
					Timeout:   2 * time.Second,
					KeepAlive: 30 * time.Second,
				}).DialContext,
				ResponseHeaderTimeout: 2 * time.Second,
				ExpectContinueTimeout: 1 * time.Second,
			},
		}
	})
	return httpClientInstance
}

func getBaseOptions(host string, port int) *redis.Options {
	return &redis.Options{
		Addr:            fmt.Sprintf("%s:%d", host, port),
		PoolSize:        poolSize,
		MinIdleConns:    minIdleConns,
		MaxRetries:      maxRetries,
		ReadTimeout:     readTimeout,
		WriteTimeout:    writeTimeout,
		PoolTimeout:     poolTimeout,
		ConnMaxIdleTime: idleTimeout,
	}
}

func createTLSConfig() (*tls.Config, error) {
	return &tls.Config{
		MinVersion:         tls.VersionTLS12,
		InsecureSkipVerify: true,
	}, nil
}

func NewClient(cfg *config.Config) (*Client, int) {
	client := &Client{
		cfg: cfg,
	}
	if err := client.connect(); err != nil {
		logger.Log.Error("Redis connetion failed", zap.Error(err))
		return nil, config.ErrRedisConnectionFailed
	}
	return client, config.ErrSuccess
}

func (c *Client) connect() error {
	c.mu.Lock()
	defer c.mu.Unlock()

	port := c.cfg.Common.Redis.Port
	if c.cfg.Common.Redis.EnableTLS {
		port = c.cfg.Common.Redis.TLSPort
	}

	baseOpts := getBaseOptions(c.cfg.Common.Redis.Host, port)

	if c.cfg.Common.Redis.Password != "" {
		baseOpts.Password = c.cfg.Common.Redis.Password
	}

	if c.cfg.Common.Redis.EnableTLS {
		tlsConfig, err := createTLSConfig()
		if err != nil {
			return err
		}
		baseOpts.TLSConfig = tlsConfig
	}

	if c.cfg.Common.Redis.EnableCluster {
		clusterOpts := &redis.ClusterOptions{
			Addrs:           []string{baseOpts.Addr},
			PoolSize:        baseOpts.PoolSize,
			MinIdleConns:    baseOpts.MinIdleConns,
			MaxRetries:      baseOpts.MaxRetries,
			ReadTimeout:     baseOpts.ReadTimeout,
			WriteTimeout:    baseOpts.WriteTimeout,
			PoolTimeout:     baseOpts.PoolTimeout,
			ConnMaxIdleTime: baseOpts.ConnMaxIdleTime,
			TLSConfig:       baseOpts.TLSConfig,
			Password:        baseOpts.Password,
		}
		c.client = redis.NewClusterClient(clusterOpts)
	} else {
		baseOpts.DB = c.cfg.Common.Redis.DB
		c.client = redis.NewClient(baseOpts)
	}

	ctx, cancel := context.WithTimeout(context.Background(), redisConnectTimeout)
	defer cancel()

	return c.client.Ping(ctx).Err()
}

func (c *Client) Close() error {
	c.mu.Lock()
	defer c.mu.Unlock()
	if c.client != nil {
		if err := c.client.Close(); err != nil {
			logger.Log.Error("Error while closing Redis client", zap.Error(err))
			return err
		}
		c.client = nil
	}
	return nil
}

func withRetry(operation func() error) error {
	var lastErr error
	for i := 0; i < maxRetries; i++ {
		if err := operation(); err == nil {
			return nil
		} else {
			lastErr = err
			backoff := time.Duration(1<<uint(i)) * retryDelay
			if backoff > 1*time.Second {
				backoff = 1 * time.Second
			}
			time.Sleep(backoff)
		}
	}
	return fmt.Errorf("operation failed after %d retries, last error: %w", maxRetries, lastErr)
}

func (c *Client) generateRedisKeys(wid, gateID string) (worldInfoKey, waitingSessionsKey, gateSessionsKey, gatesSetKey string) {
	worldInfoKey = fmt.Sprintf("world:{%s}:info", wid)
	waitingSessionsKey = fmt.Sprintf("world:{%s}:waiting:sessions", wid)
	if gateID != "" {
		gateSessionsKey = fmt.Sprintf("world:{%s}:gate:%s:sessions", wid, gateID)
	}
	gatesSetKey = fmt.Sprintf("world:{%s}:gates", wid)
	return
}

// updateActiveSessionCount 모든 게이트의 세션 수와 대기 세션 수를 합산하여 업데이트
func (c *Client) updateActiveSessionCount(ctx context.Context, wid string) error {
	worldInfoKey, waitingSessionsKey, _, gatesSetKey := c.generateRedisKeys(wid, "")

	// 활성 세션 수 계산
	gates, err := c.client.SMembers(ctx, gatesSetKey).Result()
	if err != nil && err != redis.Nil {
		return err
	}

	totalActiveSessions := int64(0)
	for _, gateID := range gates {
		_, _, gateSessionsKey, _ := c.generateRedisKeys(wid, gateID)
		count := c.client.ZCard(ctx, gateSessionsKey).Val()
		totalActiveSessions += count
	}

	// 대기 세션 수 계산
	totalWaitingSessions := c.client.ZCard(ctx, waitingSessionsKey).Val()

	_, err = c.client.TxPipelined(ctx, func(pipe redis.Pipeliner) error {
		pipe.HSet(ctx, worldInfoKey, "activeSessionCount", totalActiveSessions)
		pipe.HSet(ctx, worldInfoKey, "waitingSessionCount", totalWaitingSessions)
		return nil
	})

	return err
}

// getRealTimeActiveCount 실시간으로 활성 세션 계산
func (c *Client) getRealTimeActiveCount(ctx context.Context, wid string) (int64, error) {
	_, _, _, gatesSetKey := c.generateRedisKeys(wid, "")

	gates, err := c.client.SMembers(ctx, gatesSetKey).Result()
	if err != nil && err != redis.Nil {
		return 0, err
	}

	pipe := c.client.Pipeline()
	gateCmds := make([]*redis.IntCmd, len(gates))

	for i, gateID := range gates {
		_, _, gateSessionsKey, _ := c.generateRedisKeys(wid, gateID)
		gateCmds[i] = pipe.ZCard(ctx, gateSessionsKey)
	}
	_, err = pipe.Exec(ctx)
	if err != nil {
		return 0, fmt.Errorf("failed to get active session counts: %w", err)
	}

	totalActiveSessions := int64(0)
	for _, cmd := range gateCmds {
		totalActiveSessions += cmd.Val()
	}

	return totalActiveSessions, nil
}

// selectLeastBusyGate 가장 세션이 적은 게이트서버 선택
func (c *Client) selectLeastBusyGate(ctx context.Context, wid string) (*service.GateServer, error) {
	_, _, _, gatesSetKey := c.generateRedisKeys(wid, "")

	gates, err := c.client.SMembers(ctx, gatesSetKey).Result()
	if err != nil || len(gates) == 0 {
		return nil, fmt.Errorf("no available gate servers")
	}

	pipe := c.client.Pipeline()
	cmds := make(map[string]*redis.IntCmd)

	for _, gateID := range gates {
		_, _, gateSessionsKey, _ := c.generateRedisKeys(wid, gateID)
		cmds[gateID] = pipe.ZCard(ctx, gateSessionsKey)
	}

	_, err = pipe.Exec(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get gate session counts: %w", err)
	}

	minCount := int64(math.MaxInt64)
	var selectedGate string

	for gateID, cmd := range cmds {
		count := cmd.Val()
		if count < minCount {
			minCount = count
			selectedGate = gateID
		}
	}

	if selectedGate == "" {
		return nil, fmt.Errorf("no valid gate server found")
	}

	// gateID를 파싱해서 GateServer 구조체 반환
	parts := strings.Split(selectedGate, ":")
	if len(parts) != 3 {
		return nil, fmt.Errorf("invalid gate server ID format: %s", selectedGate)
	}

	return &service.GateServer{
		IP:   parts[0],
		Port: parts[1],
		Api:  parts[2],
	}, nil
}

func (c *Client) acquireLock(ctx context.Context, key string, duration time.Duration) (string, error) {
	deadline := time.Now().Add(duration)
	backoff := 50 * time.Millisecond
	token := uuid.NewString()
	for {
		ok, err := c.client.SetNX(ctx, key, token, duration).Result()
		if err != nil {
			return "", err
		}
		if ok {
			return token, nil
		}
		if time.Now().After(deadline) {
			return "", fmt.Errorf("failed to acquire lock %s within timeout", key)
		}

		time.Sleep(backoff)
		backoff *= 2
		if backoff > time.Second {
			backoff = time.Second
		}
	}
}

func (c *Client) releaseLock(ctx context.Context, key, token string) error {
	res, err := c.client.Eval(ctx, unlockScript, []string{key}, token).Result()
	if err != nil {
		return fmt.Errorf("failed to release lock: %w", err)
	}
	if res.(int64) == 0 {
		logger.Log.Warn("Lock release failed due to token mismatch",
			zap.String("key", key),
			zap.String("token", token))
	}
	return nil
}

func (c *Client) withLock(ctx context.Context, lockKey string, timeout time.Duration, fn func() error) error {
	return withRetry(func() error {
		token, err := c.acquireLock(ctx, lockKey, timeout)
		if err != nil {
			return err
		}
		if token == "" {
			return fmt.Errorf("could not acquire lock")
		}
		defer c.releaseLock(ctx, lockKey, token)
		return fn()
	})
}

func (c *Client) CreateSession(ctx context.Context, pID, LoginType string) (string, int) {
	ctx, cancel := context.WithTimeout(ctx, redisOperationTimeout)
	defer cancel()

	lockKey := fmt.Sprintf("lock:createsession:%s", pID)
	sessionIDRedis := fmt.Sprintf("session:%s", pID)

	var sessionTokenVal string
	err := c.withLock(ctx, lockKey, lockTimeout, func() error {
		exists, err := c.client.Exists(ctx, sessionIDRedis).Result()
		if err != nil {
			return err
		}

		sessionTokenVal = service.GenerateSessionToken()
		now := time.Now().Unix()
		authID := ""
		if LoginType == "AUTH" {
			authID = pID
		}

		pipe := c.client.Pipeline()
		if exists == 0 {
			pipe.HMSet(ctx, sessionIDRedis, map[string]interface{}{
				"sessionToken": sessionTokenVal,
				"authId":       authID,
				"status":       "inactive",
				"createdAt":    now,
				"lastActive":   now,
				"pid":          pID,
			})
			pipe.Expire(ctx, sessionIDRedis, sessionExpirationTime)
		} else {
			pipe.HSet(ctx, sessionIDRedis, "sessionToken", sessionTokenVal)
		}

		_, execErr := pipe.Exec(ctx)
		return execErr
	})
	if err != nil {
		return "", config.ErrFailedToCreateSession
	}

	return sessionTokenVal, config.ErrSuccess
}

func (c *Client) processExpiredScenario(
	ctx context.Context,
	wid, sessionID, sessionWorldID, gateServerID string,
	activeCount, maxActiveSessionCount int64,
) (*service.LoginResult, int, error) {

	if sessionWorldID != wid {
		res, code, e := c.handleInactiveSession(ctx, wid, sessionID, activeCount, maxActiveSessionCount)
		if e != nil {
			return nil, code, e
		}
		return res, code, nil
	} else {
		res, code, e := c.handleActiveSessionInSameWorld(ctx, wid, sessionID, gateServerID)
		if e != nil {
			return nil, code, e
		}
		return res, code, nil
	}
}

func (c *Client) processLoginCommon(ctx context.Context, wid, pid string, sessionInfo, worldInfo map[string]string) (*service.LoginResult, int, error) {
	maxActiveSessionCount, _ := strconv.ParseInt(worldInfo["maxActiveSessionCount"], 10, 64)
	activeCount, _ := strconv.ParseInt(worldInfo["activeSessionCount"], 10, 64)

	// 멀티프로세스 환경에서 안전하게 실시간으로 활성 세션 수 계산
	realTimeActive, err := c.getRealTimeActiveCount(ctx, wid)
	if err == nil {
		activeCount = realTimeActive
	}

	sessionStatus := sessionInfo["status"]
	gateServerID := sessionInfo["gateServerId"]
	sessionWorldID := sessionInfo["worldId"]

	sessionID := fmt.Sprintf("session:%s", pid)
	_, waitingSessionsKey, _, _ := c.generateRedisKeys(wid, "")

	switch sessionStatus {
	case "active":
		httpCh := make(chan error, 1)

		go func() {
			err := c.sendDuplicateLoginRequest(ctx, pid, gateServerID)
			httpCh <- err
		}()

		httpErr := <-httpCh
		if httpErr != nil {
			logger.Log.Error("Duplicate login HTTP request failed",
				zap.String("wid", wid),
				zap.String("pid", pid),
				zap.Error(httpErr))
		}

		return c.processExpiredScenario(ctx, wid, sessionID, sessionWorldID, gateServerID, activeCount, maxActiveSessionCount)

	case "waiting":
		res, code, e := c.handleWaitingSession(ctx, waitingSessionsKey, wid, sessionID, activeCount, maxActiveSessionCount)
		if e != nil {
			return nil, code, e
		}
		return res, code, nil

	case "inactive":
		res, code, e := c.handleInactiveSession(ctx, wid, sessionID, activeCount, maxActiveSessionCount)
		if e != nil {
			return nil, code, e
		}
		return res, code, nil

	case "expired":
		return c.processExpiredScenario(ctx, wid, sessionID, sessionWorldID, gateServerID, activeCount, maxActiveSessionCount)

	default:
		// 알 수 없는 세션 상태인 경우 세션 삭제
		logger.Log.Warn("Unknown session status, deleting session",
			zap.String("sessionID", sessionID),
			zap.String("sessionStatus", sessionStatus),
			zap.String("wid", wid),
			zap.String("pid", pid))

		err := c.client.Del(ctx, sessionID).Err()
		if err != nil {
			logger.Log.Error("Failed to delete session with unknown status",
				zap.String("sessionID", sessionID),
				zap.String("sessionStatus", sessionStatus),
				zap.Error(err))
		}

		return nil, config.ErrUnknownSessionStatus, fmt.Errorf("unknown session status: %s, session deleted", sessionStatus)
	}
}

func (c *Client) ProcessLogin(ctx context.Context, wid, pid string) (*service.LoginResult, int) {
	ctx, cancel := context.WithTimeout(ctx, redisOperationTimeout)
	defer cancel()

	worldInfoKey, _, _, _ := c.generateRedisKeys(wid, "")
	sessionID := fmt.Sprintf("session:%s", pid)

	sessionInfo, err := c.client.HGetAll(ctx, sessionID).Result()
	if err != nil || len(sessionInfo) == 0 {
		return nil, config.ErrInvalidSession
	}

	worldInfo, err := c.client.HGetAll(ctx, worldInfoKey).Result()
	if err != nil || len(worldInfo) == 0 {
		return nil, config.ErrInvalidWorld
	}

	lockKey := fmt.Sprintf("lock:login:%s:%s", wid, pid)
	var result *service.LoginResult
	var errCode = config.ErrSuccess

	err = c.withLock(ctx, lockKey, lockTimeout, func() error {
		res, code, e := c.processLoginCommon(ctx, wid, pid, sessionInfo, worldInfo)
		if e != nil {
			errCode = code
			return e
		}
		result = res
		return nil
	})
	if err != nil {
		return nil, errCode
	}
	return result, config.ErrSuccess
}

func (c *Client) ProcessLoginKG(ctx context.Context, wid, playerId string, country string, os string, osVersion string, market string, deviceInfo string) (*service.LoginResult, int) {
	ctx, cancel := context.WithTimeout(ctx, redisOperationTimeout)
	defer cancel()

	worldInfoKey, _, _, _ := c.generateRedisKeys(wid, "")
	sessionID := fmt.Sprintf("session:%s", playerId)

	sessionInfo, err := c.client.HGetAll(ctx, sessionID).Result()
	if err != nil || len(sessionInfo) == 0 {
		return nil, config.ErrInvalidSession
	}

	worldInfo, err := c.client.HGetAll(ctx, worldInfoKey).Result()
	if err != nil || len(worldInfo) == 0 {
		return nil, config.ErrInvalidWorld
	}

	originalStatus := sessionInfo["status"]

	lockKey := fmt.Sprintf("lock:login:%s:%s", wid, playerId)
	var result *service.LoginResult
	var errCode = config.ErrSuccess

	err = c.withLock(ctx, lockKey, lockTimeout, func() error {
		res, code, e := c.processLoginCommon(ctx, wid, playerId, sessionInfo, worldInfo)
		if e != nil {
			errCode = code
			return e
		}

		if originalStatus == "inactive" {
			pipe := c.client.Pipeline()
			pipe.HMSet(ctx, sessionID, map[string]interface{}{
				"playerId":   playerId,
				"country":    country,
				"os":         os,
				"osVersion":  osVersion,
				"market":     market,
				"deviceInfo": deviceInfo,
			})
			if _, pErr := pipe.Exec(ctx); pErr != nil {
				errCode = config.ErrFailedToProcessLogin
				return pErr
			}
		}

		result = res
		return nil
	})
	if err != nil {
		return nil, errCode
	}
	return result, config.ErrSuccess
}

func (c *Client) handleWaitingSession(ctx context.Context, waitingSessionsKey, wid, sessionID string, activeCount, maxActiveSessionCount int64) (*service.LoginResult, int, error) {
	position, err := c.client.ZRank(ctx, waitingSessionsKey, sessionID).Result()
	if err != nil && err != redis.Nil {
		return nil, config.ErrFailedToProcessLogin, err
	}

	availableSlots := maxActiveSessionCount - activeCount
	if position < availableSlots {
		gateServer, err := c.moveFromWaitingToActive(ctx, wid, sessionID)
		if err != nil {
			return nil, config.ErrFailedToProcessLogin, err
		}
		return &service.LoginResult{Status: service.LoginSuccess, GateServer: gateServer}, config.ErrSuccess, nil
	}

	if err := c.client.HSet(ctx, sessionID, "lastActive", time.Now().Unix()).Err(); err != nil {
		return nil, config.ErrFailedToProcessLogin, err
	}

	return &service.LoginResult{Status: service.LoginWaiting, QueuePosition: position + 1}, config.ErrSuccess, nil
}

func (c *Client) handleInactiveSession(ctx context.Context, wid, sessionID string, activeCount, maxActiveSessionCount int64) (*service.LoginResult, int, error) {
	sessionInfo, err := c.client.HGetAll(ctx, sessionID).Result()
	if err != nil {
		return nil, config.ErrFailedToProcessLogin, fmt.Errorf("failed to get session info: %w", err)
	}

	previousWorldID := sessionInfo["worldId"]
	previousGateID := sessionInfo["gateServerId"]

	if previousWorldID != "" && previousWorldID != wid {
		if err := c.cleanupPreviousWorldSession(ctx, previousWorldID, sessionID, previousGateID); err != nil {
			logger.Log.Error("Failed to cleanup previous world session",
				zap.String("previousWorld", previousWorldID),
				zap.String("newWorld", wid),
				zap.String("sessionID", sessionID),
				zap.Error(err))
		}
	}

	if activeCount < maxActiveSessionCount {
		gateServer, err := c.moveToActiveSession(ctx, wid, sessionID)
		if err != nil {
			logger.Log.Error("Failed to move session to active state",
				zap.String("sessionID", sessionID),
				zap.Error(err))
			return nil, config.ErrFailedToProcessLogin, err
		}
		return &service.LoginResult{Status: service.LoginSuccess, GateServer: gateServer}, config.ErrSuccess, nil
	}

	if err := c.moveToWaitingSession(ctx, wid, sessionID); err != nil {
		return nil, config.ErrFailedToProcessLogin, err
	}

	// 대기열에 들어갈 때만 실시간으로 대기 세션 수 계산
	_, waitingSessionsKey, _, _ := c.generateRedisKeys(wid, "")
	realTimeWaitingCount := c.client.ZCard(ctx, waitingSessionsKey).Val()

	return &service.LoginResult{Status: service.LoginWaiting, QueuePosition: realTimeWaitingCount}, config.ErrSuccess, nil
}

func (c *Client) cleanupPreviousWorldSession(ctx context.Context, previousWorldID, sessionID, previousGateID string) error {
	_, _, previousGateSessionsKey, _ := c.generateRedisKeys(previousWorldID, previousGateID)

	logger.Log.Info("Cleaning up previous world session",
		zap.String("previousWorldID", previousWorldID),
		zap.String("sessionID", sessionID),
		zap.String("previousGateID", previousGateID))

	_, err := c.client.TxPipelined(ctx, func(pipe redis.Pipeliner) error {
		// 이전 게이트의 세션 리스트에서 제거
		pipe.ZRem(ctx, previousGateSessionsKey, sessionID)
		return nil
	})

	if err != nil {
		logger.Log.Error("Failed to cleanup previous world session",
			zap.String("previousWorld", previousWorldID),
			zap.String("sessionID", sessionID),
			zap.String("previousGate", previousGateID),
			zap.Error(err))
		return fmt.Errorf("failed to cleanup previous world session: %w", err)
	}

	logger.Log.Info("Cleaned up previous world session",
		zap.String("previousWorld", previousWorldID),
		zap.String("sessionID", sessionID),
		zap.String("previousGate", previousGateID))
	return nil
}

func (c *Client) handleActiveSessionInSameWorld(ctx context.Context, wid, sessionID, gateServerID string) (*service.LoginResult, int, error) {
	gateServer, err := c.getGateServer(ctx, wid, gateServerID)
	if err != nil {
		return nil, config.ErrFailedToGetGateServer, err
	}

	setGateServerID := fmt.Sprintf("%s:%s:%s", gateServer.IP, gateServer.Port, gateServer.Api)
	now := time.Now().Unix()

	// 기존 게이트서버 정보 조회
	sessionInfo, err := c.client.HGetAll(ctx, sessionID).Result()
	if err != nil {
		return nil, config.ErrFailedToProcessLogin, fmt.Errorf("failed to get session info: %w", err)
	}

	previousGateID := sessionInfo["gateServerId"]

	_, _, currentGateSessionsKey, _ := c.generateRedisKeys(wid, setGateServerID)

	// 현재 게이트의 세션 리스트에 존재하는지 확인
	_, scoreErr := c.client.ZScore(ctx, currentGateSessionsKey, sessionID).Result()
	sessionExistsInList := (scoreErr == nil)

	// 먼저 ZAdd가 필요한지 확인하고 실행
	if previousGateID != setGateServerID || !sessionExistsInList {

		// ZAdd 먼저 실행하여 성공 여부 확인
		zaddResult := c.client.ZAdd(ctx, currentGateSessionsKey, redis.Z{Member: sessionID, Score: float64(now)})
		if zaddResult.Err() != nil {
			logger.Log.Error("Failed to add session to gate list",
				zap.String("sessionID", sessionID),
				zap.String("gateServer", setGateServerID),
				zap.Error(zaddResult.Err()))
			return nil, config.ErrFailedToProcessLogin, zaddResult.Err()
		}
	}

	// ZAdd 성공 후 나머지 작업 실행
	_, err = c.client.TxPipelined(ctx, func(pipe redis.Pipeliner) error {
		// 세션 정보 업데이트
		pipe.HMSet(ctx, sessionID, map[string]interface{}{
			"gateServerId": setGateServerID,
			"status":       "active",
			"lastActive":   now,
		})

		// 게이트서버가 변경된 경우 이전 게이트에서 제거
		if previousGateID != setGateServerID && previousGateID != "" {
			_, _, prevGateSessionsKey, _ := c.generateRedisKeys(wid, previousGateID)
			pipe.ZRem(ctx, prevGateSessionsKey, sessionID)
		}

		return nil
	})

	if err != nil {
		logger.Log.Error("Failed to handle active session in same world",
			zap.String("sessionID", sessionID),
			zap.String("gateServer", setGateServerID),
			zap.Error(err))
		return nil, config.ErrFailedToProcessLogin, err
	}

	return &service.LoginResult{Status: service.LoginSuccess, GateServer: gateServer}, config.ErrSuccess, nil
}

func (c *Client) moveToActiveSession(ctx context.Context, wid, sessionID string) (*service.GateServer, error) {
	gateServer, err := c.selectLeastBusyGate(ctx, wid)
	if err != nil {
		return nil, fmt.Errorf("failed to select gate server: %w", err)
	}

	serverID := fmt.Sprintf("%s:%s:%s", gateServer.IP, gateServer.Port, gateServer.Api)
	_, _, gateSessionsKey, _ := c.generateRedisKeys(wid, serverID)
	now := time.Now().Unix()

	// 먼저 ZAdd 실행하여 성공 여부 확인
	zaddResult := c.client.ZAdd(ctx, gateSessionsKey, redis.Z{Member: sessionID, Score: float64(now)})
	if zaddResult.Err() != nil {
		logger.Log.Error("Failed to add session to gate list",
			zap.String("sessionID", sessionID),
			zap.String("gateServer", serverID),
			zap.Error(zaddResult.Err()))
		return nil, fmt.Errorf("failed to add session to gate list: %w", zaddResult.Err())
	}

	// ZAdd 성공 후 세션 정보 업데이트
	_, err = c.client.TxPipelined(ctx, func(pipe redis.Pipeliner) error {
		pipe.HMSet(ctx, sessionID, map[string]interface{}{
			"worldId":      wid,
			"status":       "active",
			"gateServerId": serverID,
			"lastActive":   now,
		})
		return nil
	})

	if err != nil {
		logger.Log.Error("Failed to move session to active state",
			zap.String("sessionID", sessionID),
			zap.String("gateServer", serverID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to execute pipeline: %w", err)
	}

	return gateServer, nil
}

func (c *Client) moveToWaitingSession(ctx context.Context, wid, sessionID string) error {
	_, waitingSessionsKey, _, _ := c.generateRedisKeys(wid, "")
	now := time.Now().Unix()

	// 먼저 ZAdd 실행하여 성공 여부 확인
	zaddResult := c.client.ZAdd(ctx, waitingSessionsKey, redis.Z{Score: float64(now), Member: sessionID})
	if zaddResult.Err() != nil {
		logger.Log.Error("Failed to add session to waiting list",
			zap.String("sessionID", sessionID),
			zap.Error(zaddResult.Err()))
		return fmt.Errorf("failed to add session to waiting list: %w", zaddResult.Err())
	}

	// ZAdd 성공 후 세션 정보 업데이트
	_, err := c.client.TxPipelined(ctx, func(pipe redis.Pipeliner) error {
		pipe.HMSet(ctx, sessionID, map[string]interface{}{
			"worldId":      wid,
			"status":       "waiting",
			"gateServerId": "",
			"lastActive":   now,
		})
		pipe.Persist(ctx, sessionID)
		return nil
	})

	if err != nil {
		logger.Log.Error("Failed to move session to waiting state",
			zap.String("sessionID", sessionID),
			zap.Error(err))
		return fmt.Errorf("failed to execute pipeline: %w", err)
	}
	return nil
}

func (c *Client) moveFromWaitingToActive(ctx context.Context, wid, sessionID string) (*service.GateServer, error) {
	_, waitingSessionsKey, _, _ := c.generateRedisKeys(wid, "")

	gateServer, err := c.selectLeastBusyGate(ctx, wid)
	if err != nil {
		return nil, fmt.Errorf("failed to select gate server: %w", err)
	}
	serverID := fmt.Sprintf("%s:%s:%s", gateServer.IP, gateServer.Port, gateServer.Api)
	_, _, gateSessionsKey, _ := c.generateRedisKeys(wid, serverID)
	now := time.Now().Unix()

	// 먼저 ZAdd 실행하여 성공 여부 확인
	zaddResult := c.client.ZAdd(ctx, gateSessionsKey, redis.Z{Member: sessionID, Score: float64(now)})
	if zaddResult.Err() != nil {
		logger.Log.Error("Failed to add session to gate list",
			zap.String("sessionID", sessionID),
			zap.String("gateServer", serverID),
			zap.Error(zaddResult.Err()))
		return nil, fmt.Errorf("failed to add session to gate list: %w", zaddResult.Err())
	}

	// 다음으로 대기 목록에서 제거 확인
	zremResult := c.client.ZRem(ctx, waitingSessionsKey, sessionID)
	if zremResult.Err() != nil {
		// ZAdd는 성공했지만 ZRem 실패 시 롤백
		c.client.ZRem(ctx, gateSessionsKey, sessionID)
		logger.Log.Error("Failed to remove session from waiting list",
			zap.String("sessionID", sessionID),
			zap.Error(zremResult.Err()))
		return nil, fmt.Errorf("failed to remove session from waiting list: %w", zremResult.Err())
	}

	// ZAdd, ZRem 모두 성공 후 세션 정보 업데이트
	_, err = c.client.TxPipelined(ctx, func(pipe redis.Pipeliner) error {
		pipe.HMSet(ctx, sessionID, map[string]interface{}{
			"status":       "active",
			"gateServerId": serverID,
			"lastActive":   now,
		})
		return nil
	})

	if err != nil {
		logger.Log.Error("Failed to move session from waiting to active state",
			zap.String("sessionID", sessionID),
			zap.String("gateServer", serverID),
			zap.Error(err))
		return nil, fmt.Errorf("failed to execute pipeline: %w", err)
	}

	return gateServer, nil
}

func (c *Client) getGateServer(ctx context.Context, wid string, serverID string) (*service.GateServer, error) {
	_, _, _, gatesSetKey := c.generateRedisKeys(wid, "")

	// 게이트가 존재하는지 확인
	exists, err := c.client.SIsMember(ctx, gatesSetKey, serverID).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to check gate server existence: %w", err)
	}

	if exists {
		parts := strings.Split(serverID, ":")
		if len(parts) != 3 {
			return nil, fmt.Errorf("invalid gate server ID format: %s", serverID)
		}
		return &service.GateServer{
			IP:   parts[0],
			Port: parts[1],
			Api:  parts[2],
		}, nil
	}
	return c.selectLeastBusyGate(ctx, wid)
}

func (c *Client) sendDuplicateLoginRequest(ctx context.Context, pid, gateServerID string) error {
	parts := strings.Split(gateServerID, ":")
	if len(parts) != 3 {
		logger.Log.Error("Invalid gateServerID format")
		return fmt.Errorf("invalid gateServerID format")
	}

	apiPort := parts[2]
	gateServerHost := fmt.Sprintf("%s:%s", parts[0], apiPort)

	if !strings.HasPrefix(gateServerHost, "http://") && !strings.HasPrefix(gateServerHost, "https://") {
		gateServerHost = "http://" + gateServerHost
	}

	fullURL, err := url.Parse(gateServerHost)
	if err != nil {
		logger.Log.Error("Invalid gate server URL",
			zap.String("host", gateServerHost),
			zap.Error(err))
		return fmt.Errorf("invalid gate server URL: %w", err)
	}

	fullURL.Path = "/api/user/duplicateloginkick"
	fullURLStr := fullURL.String()

	httpCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	payload := map[string]interface{}{"PID": pid}
	jsonData, err := json.Marshal(payload)
	if err != nil {
		logger.Log.Error("Failed to marshal request payload",
			zap.Error(err))
		return err
	}

	req, err := http.NewRequestWithContext(httpCtx, "POST", fullURLStr, bytes.NewBuffer(jsonData))
	if err != nil {
		logger.Log.Error("Failed to create HTTP request",
			zap.Error(err))
		return err
	}
	req.Header.Set("Content-Type", "application/json")

	err = withRetry(func() error {
		resp, err := getHTTPClient().Do(req)
		if err != nil {
			return err
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			bodyBytes, _ := io.ReadAll(resp.Body)
			return fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(bodyBytes))
		}
		return nil
	})

	if err != nil {
		logger.Log.Error("Failed to send duplicate login notification",
			zap.String("url", fullURLStr),
			zap.String("pid", pid),
			zap.Error(err))
		return err
	}

	return nil
}

const worldsSetKey = "world:ids"

func (c *Client) syncWorld(ctx context.Context, wid string, servers []service.ServerInfo) error {
	worldInfoKey, _, _, gatesSetKey := c.generateRedisKeys(wid, "")

	worldInfo, err := c.client.HGetAll(ctx, worldInfoKey).Result()
	if err != nil && err != redis.Nil {
		return err
	}

	_, hasMaxCount := worldInfo["maxActiveSessionCount"]
	if !hasMaxCount {
		_, err := c.client.HMSet(ctx, worldInfoKey, map[string]interface{}{
			"maxActiveSessionCount": defaultMaxSessions,
			"activeSessionCount":    0,
			"waitingSessionCount":   0,
		}).Result()
		if err != nil {
			return err
		}
	}

	existingGates, err := c.client.SMembers(ctx, gatesSetKey).Result()
	if err != nil && err != redis.Nil {
		return err
	}

	newServers := make(map[string]struct{})
	for _, server := range servers {
		serverID := fmt.Sprintf("%s:%d:%d", server.IP, server.Port, server.API)
		newServers[serverID] = struct{}{}
	}

	var removedServers []string
	for _, existingServerID := range existingGates {
		if _, exists := newServers[existingServerID]; !exists {
			removedServers = append(removedServers, existingServerID)
		}
	}

	// 새로운 서버들을 게이트 목록에 추가
	pipe := c.client.Pipeline()
	for serverID := range newServers {
		pipe.SAdd(ctx, gatesSetKey, serverID)
	}
	pipe.SAdd(ctx, worldsSetKey, wid)

	// 제거된 서버들 정리
	for _, serverID := range removedServers {
		if err := c.deleteGateServer(ctx, wid, serverID); err != nil {
			logger.Log.Error("Failed to cleanup removed gate server",
				zap.String("wid", wid),
				zap.String("gateServerID", serverID),
				zap.Error(err))
		}
	}
	_, err = pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to sync world data: %w", err)
	}

	return nil
}

func (c *Client) SyncWithRedis(ctx context.Context, serverInfoMap map[string][]service.ServerInfo) int {
	if err := c.checkAndReconnect(ctx); err != nil {
		logger.Log.Error("Failed to check and reconnect to Redis", zap.Error(err))
		return config.ErrFailedToSyncWithRedis
	}
	leaderLockKey := "lock:global:sync"
	err := c.withLock(ctx, leaderLockKey, lockTimeout, func() error {
		worldServers := serverInfoMap

		wids, err := c.client.SMembers(ctx, worldsSetKey).Result()
		if err != nil && err != redis.Nil {
			return fmt.Errorf("failed to get members from world:ids, code: %d, err: %w", config.ErrFailedToSyncWithRedis, err)
		}

		existingWorldIDs := make(map[string]bool)
		for _, w := range wids {
			existingWorldIDs[w] = true
		}

		for wid, servers := range worldServers {
			syncLockKey := fmt.Sprintf("lock:sync:%s", wid)
			e := c.withLock(ctx, syncLockKey, lockTimeout, func() error {
				if err := c.syncWorld(ctx, wid, servers); err != nil {
					return fmt.Errorf("failed to sync world %s, code: %d, err: %w", wid, config.ErrFailedToSyncWithRedis, err)
				}

				// 정리 작업 실행
				if e := c.cleanupWaitingSessions(ctx, wid); e != nil {
					logger.Log.Error("Failed to cleanup expired sessions", zap.String("wid", wid), zap.Error(e))
				}

				if e := c.cleanupWorldSessions(ctx, wid); e != nil {
					logger.Log.Error("Failed to cleanup world sessions", zap.String("wid", wid), zap.Error(e))
				}

				// 활성 세션 카운트 업데이트
				if e := c.updateActiveSessionCount(ctx, wid); e != nil {
					logger.Log.Error("Failed to update active session count", zap.String("wid", wid), zap.Error(e))
				}

				return nil
			})
			if e != nil {
				logger.Log.Error("Failed to synchronize world",
					zap.String("wid", wid),
					zap.Error(e))
				return fmt.Errorf("failed to synchronize world %s, code: %d, err: %w", wid, config.ErrFailedToSyncWithRedis, e)
			}
			delete(existingWorldIDs, wid)
		}

		for wid := range existingWorldIDs {
			delLockKey := fmt.Sprintf("lock:delete:%s", wid)
			e := c.withLock(ctx, delLockKey, lockTimeout, func() error {
				if err := c.deleteWorld(ctx, wid); err != nil {
					return fmt.Errorf("failed to delete world %s, code: %d, err: %w", wid, config.ErrFailedToSyncWithRedis, err)
				}
				return nil
			})
			if e != nil {
				logger.Log.Error("Failed to delete world",
					zap.String("wid", wid),
					zap.Error(e))
				return fmt.Errorf("failed to delete world %s, code: %d, err: %w", wid, config.ErrFailedToSyncWithRedis, e)
			}
		}

		return nil
	})

	if err != nil {
		if err.Error() == "could not acquire lock" || strings.Contains(err.Error(), "failed to acquire lock") {
			logger.Log.Info("Another process is leader. Skipping SyncWithRedis.")
			return config.ErrSuccess
		}
		return config.ErrFailedToSyncWithRedis
	}

	return config.ErrSuccess
}

func (c *Client) deleteWorld(ctx context.Context, wid string) error {
	worldInfoKey, waitingSessionsKey, _, gatesSetKey := c.generateRedisKeys(wid, "")

	gates, err := c.client.SMembers(ctx, gatesSetKey).Result()
	if err != nil && err != redis.Nil {
		return err
	}

	for _, gateServerID := range gates {
		if err := c.deleteGateServer(ctx, wid, gateServerID); err != nil {
			logger.Log.Error("Failed to delete gate server during world deletion",
				zap.String("wid", wid),
				zap.String("gateServerID", gateServerID),
				zap.Error(err))
		}
	}

	pipe := c.client.Pipeline()
	pipe.Del(ctx, worldInfoKey)
	pipe.Del(ctx, waitingSessionsKey)
	pipe.Del(ctx, gatesSetKey)
	pipe.SRem(ctx, worldsSetKey, wid)
	_, err = pipe.Exec(ctx)

	return err
}

func (c *Client) cleanupWaitingSessions(ctx context.Context, wid string) error {
	lockKey := fmt.Sprintf("lock:cleanup:%s", wid)
	return c.withLock(ctx, lockKey, lockTimeout, func() error {
		_, waitingSessionsKey, _, _ := c.generateRedisKeys(wid, "")

		now := time.Now().Unix()
		expirationThreshold := now - int64(sessionWaitingExpireTime.Seconds())

		sessions, err := c.client.ZRange(ctx, waitingSessionsKey, 0, -1).Result()
		if err != nil {
			return err
		}

		var expiredSessions []string
		for _, sessionID := range sessions {
			lastActiveStr, err := c.client.HGet(ctx, sessionID, "lastActive").Result()
			if err == redis.Nil || err != nil {
				expiredSessions = append(expiredSessions, sessionID)
				continue
			}
			lastActive, err := strconv.ParseInt(lastActiveStr, 10, 64)
			if err != nil {
				continue
			}
			if lastActive < expirationThreshold {
				expiredSessions = append(expiredSessions, sessionID)
			}
		}

		if len(expiredSessions) > 0 {
			_, err := c.client.TxPipelined(ctx, func(pipe redis.Pipeliner) error {
				for _, sid := range expiredSessions {
					pipe.Del(ctx, sid)
					pipe.ZRem(ctx, waitingSessionsKey, sid)
				}
				return nil
			})
			if err != nil {
				logger.Log.Error("Failed to cleanup expired waiting sessions batch", zap.Error(err))
			}
		}

		return nil
	})
}

func (c *Client) cleanupWorldSessions(ctx context.Context, wid string) error {
	lockKey := fmt.Sprintf("lock:cleanup_world:%s", wid)
	return c.withLock(ctx, lockKey, lockTimeout, func() error {
		_, _, _, gatesSetKey := c.generateRedisKeys(wid, "")

		gates, err := c.client.SMembers(ctx, gatesSetKey).Result()
		if err != nil && err != redis.Nil {
			return err
		}

		for _, gateID := range gates {
			_, _, gateSessionsKey, _ := c.generateRedisKeys(wid, gateID)

			sessions, err := c.client.ZRange(ctx, gateSessionsKey, 0, -1).Result()
			if err != nil {
				continue
			}

			var expiredSessions []string
			for _, sessionID := range sessions {
				exists, err := c.client.Exists(ctx, sessionID).Result()
				if err != nil {
					continue
				}
				if exists == 0 {
					expiredSessions = append(expiredSessions, sessionID)
				}
			}

			if len(expiredSessions) > 0 {
				c.client.ZRem(ctx, gateSessionsKey, expiredSessions)
			}
		}

		return nil
	})
}

func (c *Client) deleteGateServer(ctx context.Context, wid, gateServerID string) error {
	_, _, gateSessionsKey, gatesSetKey := c.generateRedisKeys(wid, gateServerID)

	// 해당 게이트의 모든 세션 조회
	sessions, err := c.client.ZRange(ctx, gateSessionsKey, 0, -1).Result()
	if err != nil {
		return fmt.Errorf("failed to get sessions for gate server: %w", err)
	}

	_, err = c.client.TxPipelined(ctx, func(pipe redis.Pipeliner) error {
		// 게이트 목록에서 제거
		pipe.SRem(ctx, gatesSetKey, gateServerID)

		// 게이트별 세션 리스트 삭제
		pipe.Del(ctx, gateSessionsKey)

		// 개별 세션들 삭제
		for _, sid := range sessions {
			pipe.Del(ctx, sid)
		}

		return nil
	})

	if err != nil {
		logger.Log.Error("Failed to cleanup gate server",
			zap.String("wid", wid),
			zap.String("gateServerID", gateServerID),
			zap.Int("sessionsCount", len(sessions)),
			zap.Error(err))
		return err
	}

	logger.Log.Debug("Gate server deleted",
		zap.String("wid", wid),
		zap.String("gateServerID", gateServerID),
		zap.Int("sessionsDeleted", len(sessions)))

	return nil
}

func (c *Client) CancelWaiting(ctx context.Context, wid, pid string) error {
	_, waitingSessionsKey, _, _ := c.generateRedisKeys(wid, "")
	sessionID := fmt.Sprintf("session:%s", pid)

	lockKey := fmt.Sprintf("lock:cancelwaiting:%s:%s", wid, pid)
	return c.withLock(ctx, lockKey, lockTimeout, func() error {
		status, err := c.client.HGet(ctx, sessionID, "status").Result()
		if err == redis.Nil {
			return nil
		} else if err != nil {
			return err
		}
		if status != "waiting" {
			return nil
		}

		// 먼저 대기 목록에서 제거 확인
		zremResult := c.client.ZRem(ctx, waitingSessionsKey, sessionID)
		if zremResult.Err() != nil {
			logger.Log.Error("Failed to remove session from waiting list",
				zap.String("sessionID", sessionID),
				zap.Error(zremResult.Err()))
			return zremResult.Err()
		}

		// ZRem 성공 후 세션 상태 업데이트
		_, err = c.client.TxPipelined(ctx, func(pipe redis.Pipeliner) error {
			pipe.Expire(ctx, sessionID, sessionExpirationTime)
			pipe.HSet(ctx, sessionID, "status", "inactive")
			return nil
		})
		return err
	})
}

func (c *Client) checkAndReconnect(ctx context.Context) error {
	c.mu.RLock()
	_, err := c.client.Ping(ctx).Result()
	c.mu.RUnlock()

	if err == nil {
		return nil
	}
	logger.Log.Warn("Redis connection lost, attempting to reconnect")
	for i := 0; i < maxRetries; i++ {
		if err = c.connect(); err == nil {
			logger.Log.Info("Successfully reconnected to Redis")
			return nil
		}
		backoff := time.Duration(i+1) * retryDelay
		if backoff > 1*time.Second {
			backoff = 1 * time.Second
		}
		time.Sleep(backoff)
		logger.Log.Warn("Reconnection attempt failed, retrying...",
			zap.Error(err),
			zap.Int("attempt", i+1),
			zap.Duration("nextRetryIn", backoff))
	}
	return fmt.Errorf("failed to reconnect after %d attempts: %w", maxRetries, err)
}

func (c *Client) SetMaxUserCount(wid string, maxCount int64) error {
	worldInfoKey, _, _, _ := c.generateRedisKeys(wid, "")
	_, err := c.client.HSet(context.Background(), worldInfoKey, "maxActiveSessionCount", maxCount).Result()
	if err != nil {
		logger.Log.Error("Failed to set max user count",
			zap.String("wid", wid),
			zap.Int64("maxCount", maxCount),
			zap.Error(err))
		return err
	}
	return nil
}

func (c *Client) GetMaxUserCount(wid string) int64 {
	worldInfoKey, _, _, _ := c.generateRedisKeys(wid, "")
	result, err := c.client.HGetAll(context.Background(), worldInfoKey).Result()
	if err != nil {
		return defaultMaxSessions
	}

	maxCount, _ := strconv.ParseInt(result["maxActiveSessionCount"], 10, 64)
	if maxCount == 0 {
		maxCount = defaultMaxSessions
	}

	return maxCount
}

func (c *Client) GetWorldStatus(wid string) (activeCount, maxCount int64, err error) {
	worldInfoKey, _, _, _ := c.generateRedisKeys(wid, "")
	result, err := c.client.HGetAll(context.Background(), worldInfoKey).Result()
	if err != nil {
		return 0, 0, err
	}

	activeCount, _ = strconv.ParseInt(result["activeSessionCount"], 10, 64)
	maxCount = c.GetMaxUserCount(wid)

	return activeCount, maxCount, nil
}
