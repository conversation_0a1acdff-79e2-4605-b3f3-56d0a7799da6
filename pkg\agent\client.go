package agent

import (
	"NSFrontServer/config"
	"encoding/binary"
	"fmt"
	"net"
	"os"
	"strings"
	"time"

	"github.com/shirou/gopsutil/v3/process"
	"go.uber.org/zap"
)

type Client struct {
	conn    net.Conn
	process map[string]any

	logger *zap.Logger

	onTerminate func()
}

func createProcess(process_type string, process_alias string, process_class_name string, status string, config config.Config) map[string]any {
	pid := os.Getpid()
	exePath, _ := os.Executable()
	full_command_line := strings.Join(os.Args, " ")

	publicName := config.Service.PublicName
	if publicName == "" {
		publicName, _ = os.Hostname()
	}

	return map[string]any{
		"process_id":                  pid,
		"process_type":                process_type,
		"process_alias":               process_alias,
		"process_class_name":          process_class_name,
		"binary_path":                 exePath,
		"run_status":                  status,
		"full_command_line":           full_command_line,
		"server_start_time":           time.Now().Unix(),
		"process_phys_memory_usage":   0,
		"process_phys_memory_percent": 0,
		"process_cpu_usage_percent":   0,
		"peer_count":                  0,
		"kernel_recv_bytes":           0,
		"service_dispatched":          0,
		"service_send":                0,
		"kernel_send_bytes":           0,
		"database_remote_ip":          config.Common.CommonDB.Host,
		"database_remote_port":        config.Common.CommonDB.Port,
		"sid":                         0,
		"wid":                         config.Service.Wid,
		"server_environment":          "production",
		"service_public_name":         publicName,
		"service_public_domain":       "",
		"service_public_port":         config.Application.Port,
		"consul_remote_ip":            config.Consul.Host,
		"consul_remote_port":          config.Consul.Port,
		"update_elapsed_time":         0,
	}
}

func getProcessResourceUsage() (physMem uint64, memPercent float32, cpuPercent float64, err error) {
	p, err := process.NewProcess(int32(os.Getpid()))
	if err != nil {
		return
	}
	memInfo, err := p.MemoryInfo()
	if err != nil {
		return
	}
	physMem = memInfo.RSS

	memPercent, err = p.MemoryPercent()
	if err != nil {
		return
	}

	cpuPercent, err = p.CPUPercent()
	if err != nil {
		return
	}
	return
}

func NewClient(classname string, config config.Config, logger *zap.Logger, onTerminate func()) *Client {
	return &Client{
		process: createProcess(
			"front_server",
			"FrontServer",
			classname,
			"BLANK",
			config,
		),
		logger:      logger,
		onTerminate: onTerminate,
	}
}

func (c *Client) Connect(host string, timeout time.Duration) error {
	var err error
	c.conn, err = net.DialTimeout("tcp", host, timeout)
	if err != nil {
		return err
	}

	go c.recvLoop()

	return nil
}

func (c *Client) recvLoop() {
	defer c.Close()
	for {
		header := make([]byte, 4)
		_, err := c.conn.Read(header)
		if err != nil {
			c.logger.Error(fmt.Sprintf("Read header error: %v", err))
			return
		}

		packetSize := binary.LittleEndian.Uint32(header)
		if packetSize < 4 {
			c.logger.Error(fmt.Sprintf("Invalid packet size: %d", packetSize))
			return
		}

		body := make([]byte, packetSize-4)
		_, err = c.conn.Read(body)
		if err != nil {
			c.logger.Error(fmt.Sprintf("Read body error: %v", err))
			return
		}

		packet, err := CreateRecvPacket(body)
		if err != nil {
			c.logger.Error(fmt.Sprintf("CreateRecvPacket error: %v", err))
			return
		}
		c.logger.Info(fmt.Sprintf("Received packet: %s", string(packet.PacketType)))

		payload, err := packet.ReadJson(packet.GetPayloadOffset())
		if err != nil {
			c.logger.Error(fmt.Sprintf("ReadJson error: %v", err))
			return
		}

		c.logger.Info(fmt.Sprintf("Received JSON: %s", payload["payload_type"]))

		if payload["payload_type"] == "JSON_PAYLOAD_TYPE_TERMINATE_PROCESS" {
			c.logger.Info("Received terminate process command")
			if c.onTerminate != nil {
				c.onTerminate()
			}
			return
		}
	}
}

func (c *Client) Close() {
	if c.conn != nil {
		c.conn.Close()
	}
}

func (c *Client) IsConnected() bool {
	return c.conn != nil
}

func (c *Client) ChangeStatus(status string) error {
	c.process["run_status"] = status
	return c.SendChangeStatus()
}

func (c *Client) Update(elapsed_time time.Duration) error {
	physMem, memPercent, cpuPercent, err := getProcessResourceUsage()
	if err == nil {
		c.process["process_phys_memory_usage"] = physMem
		c.process["process_phys_memory_percent"] = fmt.Sprintf("%.2f", memPercent)
		c.process["process_cpu_usage_percent"] = fmt.Sprintf("%.2f", cpuPercent)
		c.process["update_elapsed_time"] = elapsed_time.Nanoseconds()
	}

	return c.SendFrontServerInfo()
}

func (c *Client) SendHello() error {
	var err error
	if c.conn == nil {
		return fmt.Errorf("connection is nil")
	}

	packet := CreateHello(c.process)

	_, err = c.conn.Write(packet.SetHeader())
	return err
}

func (c *Client) SendChangeStatus() error {
	var err error
	if c.conn == nil {
		return fmt.Errorf("connection is nil")
	}

	status, ok := c.process["run_status"].(string)
	if !ok {
		return fmt.Errorf("status is not a string")
	}
	packet := CreateChangeStatus(status)

	_, err = c.conn.Write(packet.SetHeader())
	return err
}

func (c *Client) SendMessageToLocalAgent(message string) error {
	var err error
	if c.conn == nil {
		return fmt.Errorf("connection is nil")
	}

	packet := CreateMessageToLocalAgent(message)

	_, err = c.conn.Write(packet.SetHeader())
	return err
}

func (c *Client) SendFrontServerInfo() error {
	var err error
	if c.conn == nil {
		return fmt.Errorf("connection is nil")
	}

	packet := CreateFrontServerInfo(c.process, map[string]any{})

	_, err = c.conn.Write(packet.SetHeader())
	return err
}
