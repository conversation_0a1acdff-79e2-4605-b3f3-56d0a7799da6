package http

import (
	"context"
	"fmt"
	"time"

	"NSFrontServer/config"
	"NSFrontServer/logger"
	"NSFrontServer/mysqlutil"
	"NSFrontServer/redisutil"
	"NSFrontServer/service"

	"github.com/valyala/fasthttp"
	"go.uber.org/zap"
)

type Server struct {
	config        *config.Config
	service       *service.Service
	redis         *redisutil.Client
	mysql         *mysqlutil.Client
	responseStats *ResponseTimeStats
	server        *fasthttp.Server
}

func NewServer(cfg *config.Config, svc *service.Service, redis *redisutil.Client, mysql *mysqlutil.Client) *Server {
	return &Server{
		config:        cfg,
		service:       svc,
		redis:         redis,
		mysql:         mysql,
		responseStats: &ResponseTimeStats{},
	}
}

func (s *Server) measureResponseTime(next fasthttp.RequestHandler) fasthttp.RequestHandler {
	return func(ctx *fasthttp.RequestCtx) {
		start := time.Now()
		next(ctx)
		duration := time.Since(start)
		s.responseStats.Add(duration)
	}
}

func (s *Server) Initialize(ctx context.Context) int {
	logger.Log.Info("Initializing HTTP server")

	s.server = &fasthttp.Server{
		Handler:            s.router,
		ReadTimeout:        5 * time.Second,
		WriteTimeout:       10 * time.Second,
		IdleTimeout:        120 * time.Second,
		MaxRequestBodySize: 4 * 1024 * 1024, // 4MB
	}

	s.server.Handler = s.measureResponseTime(s.server.Handler)

	return config.ErrSuccess
}

func (s *Server) Start(ctx context.Context) int {
	go func() {
		if err := s.server.ListenAndServe(fmt.Sprintf(":%d", s.config.Application.Port)); err != nil {
			logger.Log.Error("HTTP server start failed", zap.Error(err))
		}
	}()

	return config.ErrSuccess
}

func (s *Server) Shutdown(ctx context.Context) int {
	if err := s.server.ShutdownWithContext(ctx); err != nil {
		logger.Log.Error("HTTP server shutdown error", zap.Error(err))
		return config.ErrFailedToShutdownServer
	}
	return config.ErrSuccess
}

func (s *Server) GetStats() (avg, min, max time.Duration) {
	return s.responseStats.Stats()
}

func (s *Server) router(ctx *fasthttp.RequestCtx) {
	switch string(ctx.Path()) {
	case "/heartbeat":
		s.handleHeartBeat(ctx)
	case "/account":
		s.handleAccount(ctx)
	case "/state":
		s.handleState(ctx)
	case "/auth/steam":
		s.handleSteamAuth(ctx)
	case "/auth/kg":
		s.handleKGAuth(ctx)
	case "/login":
		s.handleLogin(ctx)
	case "/loginKG":
		s.handleLoginKG(ctx)
	case "/loginDev":
		s.handleLoginDev(ctx)
	case "/CancelWaiting":
		s.handleCancelWaiting(ctx)
	case "/metrics":
		s.handleMetrics(ctx)
	case "/characterworlds":
		s.handleCharacterWorlds(ctx)
	default:
		ctx.Error("Not Found", fasthttp.StatusNotFound)
	}
}

func setCommonHeaders(ctx *fasthttp.RequestCtx) {
	ctx.Response.Header.Set("Content-Type", "application/json")
	ctx.Response.Header.Set("Access-Control-Allow-Origin", "*")
}
