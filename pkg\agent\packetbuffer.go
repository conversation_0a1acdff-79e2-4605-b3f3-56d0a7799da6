package agent

import (
	"bytes"
	"encoding/binary"
	"encoding/json"
	"fmt"
)

type PacketBuffer struct {
	buf *bytes.Buffer
}

func NewPacketBuffer() *PacketBuffer {
	return &PacketBuffer{
		buf: new(bytes.Buffer),
	}
}

func (b *PacketBuffer) WriteUint64(v uint64) error {
	return binary.Write(b.buf, binary.LittleEndian, v)
}

func (b *PacketBuffer) WriteUint32(v uint32) error {
	return binary.Write(b.buf, binary.LittleEndian, v)
}

func (b *PacketBuffer) WriteUint16(v uint16) error {
	return binary.Write(b.buf, binary.LittleEndian, v)
}

func (b *PacketBuffer) WriteString(s string) error {
	length := uint64(len(s))
	if err := b.WriteUint64(length); err != nil {
		return err
	}
	_, err := b.buf.Write([]byte(s))
	return err
}

func (b *PacketBuffer) WriteBytes(data []byte) error {
	length := uint64(len(data))
	if err := b.WriteUint64(length); err != nil {
		return err
	}
	_, err := b.buf.Write(data)
	return err
}

func (b *PacketBuffer) WriteJson(document map[string]any) error {
	bytes, err := json.Marshal(document)
	if err != nil {
		return err
	}
	return b.WriteBytes(bytes)
}

func (b *PacketBuffer) ReadUint64(offset int) (uint64, error) {
	if offset+8 > b.buf.Len() {
		return 0, fmt.Errorf("offset %d out of range", offset)
	}

	return binary.LittleEndian.Uint64(b.buf.Bytes()[offset : offset+8]), nil
}

func (b *PacketBuffer) ReadUint32(offset int) (uint32, error) {
	if offset+4 > b.buf.Len() {
		return 0, fmt.Errorf("offset %d out of range", offset)
	}
	return binary.LittleEndian.Uint32(b.buf.Bytes()[offset : offset+4]), nil
}

func (b *PacketBuffer) ReadUint16(offset int) (uint16, error) {
	if offset+2 > b.buf.Len() {
		return 0, fmt.Errorf("offset %d out of range", offset)
	}
	return binary.LittleEndian.Uint16(b.buf.Bytes()[offset : offset+2]), nil
}

func (b *PacketBuffer) ReadString(offset int) (string, error) {
	bytes, err := b.ReadBytes(offset)
	if err != nil {
		return "", err
	}

	return string(bytes), nil
}

func (b *PacketBuffer) ReadBytes(offset int) ([]byte, error) {
	if offset+8 > b.buf.Len() {
		return nil, fmt.Errorf("offset %d out of range", offset)
	}
	len := binary.LittleEndian.Uint64(b.buf.Bytes()[offset : offset+8])
	if offset+8+int(len) > b.buf.Len() {
		return nil, fmt.Errorf("offset %d out of range", offset)
	}
	return b.buf.Bytes()[offset+8 : offset+8+int(len)], nil
}

func (b *PacketBuffer) ReadJson(offset int) (map[string]any, error) {
	str, err := b.ReadString(offset)
	if err != nil {
		return nil, err
	}

	var result map[string]any
	err = json.Unmarshal([]byte(str), &result)
	if err != nil {
		return nil, fmt.Errorf("json.Unmarshal error: %v", err)
	}

	return result, nil
}

func (b *PacketBuffer) Bytes() []byte {
	return b.buf.Bytes()
}

func (b *PacketBuffer) Len() int {
	return b.buf.Len()
}

func (b *PacketBuffer) Reset() {
	b.buf.Reset()
}
