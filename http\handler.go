package http

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"NSFrontServer/config"
	"NSFrontServer/logger"
	"NSFrontServer/service"

	"github.com/valyala/fasthttp"
	"go.uber.org/zap"
)

func (s *Server) handleHeartBeat(ctx *fasthttp.RequestCtx) {
	setCommonHeaders(ctx)
	ctx.SetStatusCode(fasthttp.StatusOK)
	ctx.SetBody([]byte("{}"))
}

func (s *Server) handleAccount(ctx *fasthttp.RequestCtx) {
	logger.Log.Info("Handling /account request")
	setCommonHeaders(ctx)
	ctx.SetStatusCode(fasthttp.StatusOK)
	ctx.SetBody([]byte("{}"))
}

func (s *Server) handleState(ctx *fasthttp.RequestCtx) {
	setCommonHeaders(ctx)

	// 캐시된 서버 정보 사용 (이미 필터링된 정보)
	serverInfos, err := s.service.GetCachedServerInfosFromCache()
	if err != nil {
		logger.Log.Error("Failed to get cached server infos",
			zap.Error(err),
			zap.Int("errorCode", config.ErrNoAvailableGateServer),
			zap.String("errorMessage", config.ErrorMessages[config.ErrNoAvailableGateServer]),
			zap.String("clientIP", ctx.RemoteIP().String()))
		respondWithError(ctx, fasthttp.StatusInternalServerError, config.ErrNoAvailableGateServer)
		return
	}

	if len(serverInfos) == 0 {
		logger.Log.Error("No cached server infos found",
			zap.Int("errorCode", config.ErrNoAvailableGateServer),
			zap.String("errorMessage", config.ErrorMessages[config.ErrNoAvailableGateServer]),
			zap.String("clientIP", ctx.RemoteIP().String()))
		// 캐시에 정보가 없으면 에러 반환
		respondWithError(ctx, fasthttp.StatusInternalServerError, config.ErrNoAvailableGateServer)
		return
	}

	clientIP := ctx.RemoteIP().String()

	response := struct {
		Infos []service.SimplifiedServerInfo `json:"Infos"`
	}{
		Infos: s.filterServerInfos(serverInfos, clientIP),
	}

	respondWithJSON(ctx, fasthttp.StatusOK, response)
}

func (s *Server) handleSteamAuth(ctx *fasthttp.RequestCtx) {
	setCommonHeaders(ctx)
	logger.Log.Info("Handling /auth/steam request")
	var requestBody struct {
		Ticket     []int  `json:"ticket"`
		SteamAppID string `json:"steamappid"`
	}

	if err := json.Unmarshal(ctx.PostBody(), &requestBody); err != nil {
		respondWithError(ctx, fasthttp.StatusBadRequest, config.ErrInvalidRequestBody)
		return
	}

	if len(requestBody.Ticket) == 0 {
		respondWithError(ctx, fasthttp.StatusBadRequest, config.ErrMissingRequiredField)
		return
	}

	appId := s.config.Application.Profile.DefaultSteamAppId
	if requestBody.SteamAppID != "" {
		appId = requestBody.SteamAppID
	}

	var ticketData strings.Builder
	for _, b := range requestBody.Ticket {
		ticketData.WriteString(fmt.Sprintf("%02x", b))
	}

	steamResponse, errCode := s.service.AuthenticateSteamTicket(ctx, ticketData.String(), appId)
	if errCode != config.ErrSuccess {
		logger.Log.Error("Failed to authenticate with Steam",
			zap.Int("errorCode", errCode),
			zap.String("errorMessage", config.ErrorMessages[errCode]),
			zap.String("clientIP", ctx.RemoteIP().String()),
			zap.String("appId", appId))
		respondWithError(ctx, fasthttp.StatusInternalServerError, errCode)
		return
	}

	if steamResponse.Response.Error.ErrorCode != 0 {
		logger.Log.Error("Steam API returned an error",
			zap.Int("errorCode", steamResponse.Response.Error.ErrorCode),
			zap.String("errorDesc", steamResponse.Response.Error.ErrorDesc),
			zap.Ints("bytedata", requestBody.Ticket),
			zap.String("ticketdata", ticketData.String()),
			zap.String("clientIP", ctx.RemoteIP().String()),
			zap.String("appId", appId))
		respondWithError(ctx, fasthttp.StatusBadRequest, config.ErrSteamAPIError)
		return
	}

	if steamResponse.Response.Params.Result == "OK" {
		s.handleSuccessfulSteamAuth(ctx, steamResponse)
	} else {
		logger.Log.Error("Steam authentication failed",
			zap.String("result", steamResponse.Response.Params.Result))
		respondWithError(ctx, fasthttp.StatusBadRequest, config.ErrSteamAuthFailed)
	}
}

func (s *Server) handleKGAuth(ctx *fasthttp.RequestCtx) {
	setCommonHeaders(ctx)
	logger.Log.Info("Handling /auth/kg request")
	var requestBody struct {
		AccessToken string `json:"AccessToken"`
		PlayerId    string `json:"PlayerId"`
		KGAppID     string `json:"kgappid"`
		Platform    string `json:"Platform"`
	}

	if err := json.Unmarshal(ctx.PostBody(), &requestBody); err != nil {
		respondWithError(ctx, fasthttp.StatusBadRequest, config.ErrInvalidRequestBody)
		return
	}

	if requestBody.AccessToken == "" || requestBody.PlayerId == "" {
		respondWithError(ctx, fasthttp.StatusBadRequest, config.ErrMissingRequiredField)
		return
	}

	appId := s.config.Application.Profile.DefaultKGAppId
	if requestBody.KGAppID != "" {
		appId = requestBody.KGAppID
	}

	platform := s.config.Application.Profile.KGApps[appId].Platform
	if requestBody.Platform != "" {
		platform = requestBody.Platform
	}

	kgResponse, errCode := s.service.AuthenticateKGToken(ctx, requestBody.AccessToken, requestBody.PlayerId, appId, platform)
	if errCode != config.ErrSuccess {
		logger.Log.Error("Failed to authenticate with KG",
			zap.Int("errorCode", errCode),
			zap.String("errorMessage", config.ErrorMessages[errCode]),
			zap.String("clientIP", ctx.RemoteIP().String()),
			zap.String("playerId", requestBody.PlayerId),
			zap.String("appId", appId),
			zap.String("platform", platform))
		respondWithError(ctx, fasthttp.StatusInternalServerError, errCode)
		return
	}

	if kgResponse.Desc != "" {
		logger.Log.Error("KG API returned an error",
			zap.String("errorDesc", kgResponse.Desc),
			zap.String("playerId", requestBody.PlayerId),
			zap.String("appId", appId),
			zap.String("platform", platform),
			zap.String("clientIP", ctx.RemoteIP().String()),
			zap.Int("errorCode", config.ErrKGAPIError),
			zap.String("errorMessage", config.ErrorMessages[config.ErrKGAPIError]))
		respondWithError(ctx, fasthttp.StatusBadRequest, config.ErrKGAPIError)
		return
	}

	s.handleSuccessfulKGAuth(ctx, kgResponse)
}

func (s *Server) handleLogin(ctx *fasthttp.RequestCtx) {
	setCommonHeaders(ctx)
	logger.Log.Info("Handling /login request")
	var requestBody struct {
		WID          string `json:"WID"`
		PID          string `json:"PID"`
		SessionToken string `json:"SessionKey"`
	}

	if err := json.Unmarshal(ctx.PostBody(), &requestBody); err != nil {
		respondWithError(ctx, fasthttp.StatusBadRequest, config.ErrInvalidRequestBody)
		return
	}

	if requestBody.WID == "" || requestBody.SessionToken == "" || requestBody.PID == "" {
		respondWithError(ctx, fasthttp.StatusBadRequest, config.ErrMissingRequiredField)
		return
	}

	// 월드가 점검 중인지 확인
	if !s.checkWorldMaintenance(ctx, requestBody.WID, requestBody.PID) {
		return
	}

	result, errCode := s.redis.ProcessLogin(ctx, requestBody.WID, requestBody.PID)
	if errCode != config.ErrSuccess || result == nil {
		if result == nil && errCode == 0 {
			errCode = config.ErrRedisConnectionFailed
		}
		logger.Log.Error("Failed to process login",
			zap.Int("errorCode", errCode),
			zap.String("errorMessage", config.ErrorMessages[errCode]),
			zap.String("clientIP", ctx.RemoteIP().String()),
			zap.String("wid", requestBody.WID),
			zap.String("pid", requestBody.PID))
		respondWithError(ctx, fasthttp.StatusInternalServerError, errCode)
		return
	}

	switch result.Status {
	case service.LoginSuccess:

		if result.GateServer == nil {
			logger.Log.Error("Login successful but GateServer is nil", zap.String("steamID", result.SteamID))
			respondWithError(ctx, fasthttp.StatusInternalServerError, config.ErrNoAvailableGateServer)
			return
		}
		respondWithJSON(ctx, fasthttp.StatusOK, map[string]interface{}{
			"success":    true,
			"sessionkey": requestBody.SessionToken,
			"gate": map[string]interface{}{
				"ip":   result.GateServer.IP,
				"port": result.GateServer.Port,
			},
		})
	case service.LoginWaiting:
		respondWithJSON(ctx, fasthttp.StatusOK, map[string]interface{}{
			"success":  false,
			"message":  "Waiting in queue",
			"position": result.QueuePosition,
		})
	case service.LoginInvalid:
		respondWithError(ctx, fasthttp.StatusUnauthorized, config.ErrInvalidSession)
	default:
		logger.Log.Error("Unknown login status", zap.Int("status", int(result.Status)))
		respondWithError(ctx, fasthttp.StatusInternalServerError, config.ErrUnknownSessionStatus)
	}
}

func (s *Server) handleLoginKG(ctx *fasthttp.RequestCtx) {
	setCommonHeaders(ctx)
	logger.Log.Info("Handling /loginKG request")
	var requestBody struct {
		WID          string `json:"WID"`
		PlayerId     string `json:"PlayerId"`
		Country      string `json:"Country"`
		OS           string `json:"OS"`
		OSVersion    string `json:"OSVersion"`
		Market       string `json:"Market"`
		DeviceInfo   string `json:"DeviceInfo"`
		SessionToken string `json:"SessionKey"`
	}

	if err := json.Unmarshal(ctx.PostBody(), &requestBody); err != nil {
		respondWithError(ctx, fasthttp.StatusBadRequest, config.ErrInvalidRequestBody)
		return
	}

	if requestBody.WID == "" || requestBody.SessionToken == "" || requestBody.PlayerId == "" {
		respondWithError(ctx, fasthttp.StatusBadRequest, config.ErrMissingRequiredField)
		return
	}

	// 월드가 점검 중인지 확인
	if !s.checkWorldMaintenance(ctx, requestBody.WID, requestBody.PlayerId) {
		return
	}

	result, errCode := s.redis.ProcessLoginKG(ctx, requestBody.WID, requestBody.PlayerId,
		requestBody.Country, requestBody.OS, requestBody.OSVersion, requestBody.Market, requestBody.DeviceInfo)
	if errCode != config.ErrSuccess || result == nil {
		if result == nil && errCode == 0 {
			errCode = config.ErrRedisConnectionFailed
		}
		logger.Log.Error("Failed to process KG login",
			zap.Int("errorCode", errCode),
			zap.String("errorMessage", config.ErrorMessages[errCode]),
			zap.String("clientIP", ctx.RemoteIP().String()),
			zap.String("wid", requestBody.WID),
			zap.String("playerId", requestBody.PlayerId),
			zap.String("country", requestBody.Country),
			zap.String("os", requestBody.OS))
		respondWithError(ctx, fasthttp.StatusInternalServerError, errCode)
		return
	}

	switch result.Status {
	case service.LoginSuccess:
		if result.GateServer == nil {
			logger.Log.Error("Login successful but GateServer is nil", zap.String("steamID", result.SteamID))
			respondWithError(ctx, fasthttp.StatusInternalServerError, config.ErrNoAvailableGateServer)
			return
		}
		respondWithJSON(ctx, fasthttp.StatusOK, map[string]interface{}{
			"success":    true,
			"playerId":   requestBody.PlayerId,
			"sessionkey": requestBody.SessionToken,
			"gate": map[string]interface{}{
				"ip":   result.GateServer.IP,
				"port": result.GateServer.Port,
			},
		})
	case service.LoginWaiting:
		respondWithJSON(ctx, fasthttp.StatusOK, map[string]interface{}{
			"success":  false,
			"message":  "Waiting in queue",
			"position": result.QueuePosition,
		})
	case service.LoginInvalid:
		respondWithError(ctx, fasthttp.StatusUnauthorized, config.ErrInvalidSession)
	default:
		logger.Log.Error("Unknown login status", zap.Int("status", int(result.Status)))
		respondWithError(ctx, fasthttp.StatusInternalServerError, config.ErrUnknownSessionStatus)
	}
}

func (s *Server) handleLoginDev(ctx *fasthttp.RequestCtx) {
	setCommonHeaders(ctx)
	logger.Log.Info("Handling /loginDev request")
	var requestBody struct {
		WID string `json:"wid"`
		PID string `json:"pid"`
	}

	if err := json.Unmarshal(ctx.PostBody(), &requestBody); err != nil {
		respondWithError(ctx, fasthttp.StatusBadRequest, config.ErrInvalidRequestBody)
		return
	}

	if requestBody.WID == "" || requestBody.PID == "" {
		respondWithError(ctx, fasthttp.StatusBadRequest, config.ErrMissingRequiredField)
		return
	}

	// 월드가 점검 중인지 확인
	if !s.checkWorldMaintenance(ctx, requestBody.WID, requestBody.PID) {
		return
	}

	sessionToken, errCode := s.redis.CreateSession(ctx, requestBody.PID, "DEV")
	if errCode != config.ErrSuccess {
		logger.Log.Error("Failed to create temporary session",
			zap.Int("errorCode", errCode),
			zap.String("errorMessage", config.ErrorMessages[errCode]),
			zap.String("clientIP", ctx.RemoteIP().String()),
			zap.String("pid", requestBody.PID))
		respondWithError(ctx, fasthttp.StatusInternalServerError, errCode)
		return
	}

	result, errCode := s.redis.ProcessLogin(ctx, requestBody.WID, requestBody.PID)
	if errCode != config.ErrSuccess {
		logger.Log.Error("Failed to process dev login",
			zap.Int("errorCode", errCode),
			zap.String("errorMessage", config.ErrorMessages[errCode]),
			zap.String("clientIP", ctx.RemoteIP().String()),
			zap.String("wid", requestBody.WID),
			zap.String("pid", requestBody.PID))
		respondWithError(ctx, fasthttp.StatusInternalServerError, errCode)
		return
	}

	switch result.Status {
	case service.LoginSuccess:
		if result.GateServer == nil {
			logger.Log.Error("Login successful but GateServer is nil", zap.String("steamID", result.SteamID))
			respondWithError(ctx, fasthttp.StatusInternalServerError, config.ErrNoAvailableGateServer)
			return
		}
		respondWithJSON(ctx, fasthttp.StatusOK, map[string]interface{}{
			"success":    true,
			"sessionkey": sessionToken,
			"gate": map[string]interface{}{
				"ip":   result.GateServer.IP,
				"port": result.GateServer.Port,
			},
		})
	case service.LoginWaiting:
		respondWithJSON(ctx, fasthttp.StatusOK, map[string]interface{}{
			"success":  false,
			"message":  "Waiting in queue",
			"position": result.QueuePosition,
		})
	case service.LoginInvalid:
		respondWithError(ctx, fasthttp.StatusUnauthorized, config.ErrInvalidSession)
	default:
		logger.Log.Error("Unknown login status", zap.Int("status", int(result.Status)))
		respondWithError(ctx, fasthttp.StatusInternalServerError, config.ErrUnknownSessionStatus)
	}
}

func (s *Server) handleCancelWaiting(ctx *fasthttp.RequestCtx) {
	setCommonHeaders(ctx)
	logger.Log.Info("Handling /CancelWaiting request")
	var requestBody struct {
		WID          string `json:"WID"`
		PID          string `json:"PID"`
		SessionToken string `json:"SessionKey"`
	}

	if err := json.Unmarshal(ctx.PostBody(), &requestBody); err != nil {
		respondWithError(ctx, fasthttp.StatusBadRequest, config.ErrInvalidRequestBody)
		return
	}

	if requestBody.WID == "" || requestBody.SessionToken == "" {
		respondWithError(ctx, fasthttp.StatusBadRequest, config.ErrMissingRequiredField)
		return
	}

	err := s.redis.CancelWaiting(ctx, requestBody.WID, requestBody.PID)
	if err != nil {
		respondWithError(ctx, fasthttp.StatusInternalServerError, config.ErrInvalidSession)
		return
	}

	respondWithJSON(ctx, fasthttp.StatusOK, map[string]interface{}{
		"success":    true,
		"sessionKey": requestBody.SessionToken,
	})
}

func (s *Server) handleCharacterWorlds(ctx *fasthttp.RequestCtx) {
	setCommonHeaders(ctx)
	logger.Log.Info("Handling /characterworlds request")

	var requestBody struct {
		PlatformID string `json:"PlatformID"`
	}

	if err := json.Unmarshal(ctx.PostBody(), &requestBody); err != nil {
		logger.Log.Error("Failed to unmarshal request body",
			zap.Error(err),
			zap.String("clientIP", ctx.RemoteIP().String()))
		respondWithError(ctx, fasthttp.StatusBadRequest, config.ErrInvalidRequestBody)
		return
	}

	if requestBody.PlatformID == "" {
		logger.Log.Error("Missing PlatformID in request",
			zap.String("clientIP", ctx.RemoteIP().String()))
		respondWithError(ctx, fasthttp.StatusBadRequest, config.ErrMissingRequiredField)
		return
	}

	// 데이터베이스에서 캐릭터 월드 목록 조회
	dbCtx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()

	worldIDs, err := s.mysql.GetCharacterWorlds(dbCtx, requestBody.PlatformID)
	if err != nil {
		logger.Log.Error("Failed to get character worlds from database",
			zap.Error(err),
			zap.String("platformID", requestBody.PlatformID),
			zap.String("clientIP", ctx.RemoteIP().String()))
		respondWithError(ctx, fasthttp.StatusInternalServerError, config.ErrMySQLQueryFailed)
		return
	}

	logger.Log.Info("Successfully retrieved character worlds",
		zap.String("platformID", requestBody.PlatformID),
		zap.Strings("worldIDs", worldIDs),
		zap.Int("count", len(worldIDs)),
		zap.String("clientIP", ctx.RemoteIP().String()))

	// 응답 구조체
	response := struct {
		WorldIDs []string `json:"WorldIDs"`
	}{
		WorldIDs: worldIDs,
	}

	respondWithJSON(ctx, fasthttp.StatusOK, response)
}

func (s *Server) filterServerInfos(serverInfos []service.SimplifiedServerInfo, clientIP string) []service.SimplifiedServerInfo {
	config := s.service.GetConfig()
	if s.contains(config.WhiteList, clientIP) || s.contains(config.WhiteList, "0.0.0.0") {
		return append(serverInfos, config.CustomInfos...)
	}
	return serverInfos
}

func (s *Server) contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// checkWorldMaintenance 월드 점검 상태를 확인하고 필요시 계정 존재 여부를 검증
func (s *Server) checkWorldMaintenance(ctx *fasthttp.RequestCtx, wid, pid string) bool {
	if !s.service.IsWorldUnderMaintenance(wid) {
		return true // 점검 중이 아니면 통과
	}

	logger.Log.Info("World is under maintenance",
		zap.String("wid", wid),
		zap.String("pid", pid))

	// PID가 존재하는지 확인
	dbCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	exists, err := s.mysql.CheckAccountExists(dbCtx, pid)
	if err != nil {
		logger.Log.Error("Failed to check account existence",
			zap.String("pid", pid),
			zap.Error(err))
		// 오류가 발생하면 점검 중 에러 반환
		respondWithError(ctx, fasthttp.StatusForbidden, config.ErrWorldUnderMaintenance)
		return false
	}

	if !exists {
		// PID가 존재하지 않으면 점검 중 에러 반환
		logger.Log.Info("Account does not exist during maintenance",
			zap.String("pid", pid))
		respondWithError(ctx, fasthttp.StatusForbidden, config.ErrWorldUnderMaintenance)
		return false
	}

	// PID가 존재하면 로그인 진행
	logger.Log.Info("Account exists, allowing login during maintenance",
		zap.String("pid", pid))
	return true
}

func (s *Server) handleSuccessfulSteamAuth(ctx *fasthttp.RequestCtx, steamResponse *service.SteamResponse) {
	steamID := steamResponse.Response.Params.SteamID
	sessionToken, errCode := s.redis.CreateSession(ctx, steamID, "AUTH")
	if errCode != config.ErrSuccess {
		logger.Log.Error("Failed to create temporary session for Steam auth",
			zap.Int("errorCode", errCode),
			zap.String("errorMessage", config.ErrorMessages[errCode]),
			zap.String("clientIP", ctx.RemoteIP().String()),
			zap.String("steamID", steamID))
		respondWithError(ctx, fasthttp.StatusInternalServerError, errCode)
		return
	}

	respondWithJSON(ctx, fasthttp.StatusOK, map[string]interface{}{
		"success":    true,
		"sessionKey": sessionToken,
	})
}

func (s *Server) handleSuccessfulKGAuth(ctx *fasthttp.RequestCtx, kgResponse *service.KGResponse) {
	playerId := kgResponse.Player.PayerId
	sessionToken, errCode := s.redis.CreateSession(ctx, playerId, "AUTH")
	if errCode != config.ErrSuccess {
		logger.Log.Error("Failed to create temporary session for KG auth",
			zap.Int("errorCode", errCode),
			zap.String("errorMessage", config.ErrorMessages[errCode]),
			zap.String("clientIP", ctx.RemoteIP().String()),
			zap.String("playerId", playerId))
		respondWithError(ctx, fasthttp.StatusInternalServerError, errCode)
		return
	}

	respondWithJSON(ctx, fasthttp.StatusOK, map[string]interface{}{
		"success":    true,
		"sessionKey": sessionToken,
	})
}

func (s *Server) handleMetrics(ctx *fasthttp.RequestCtx) {
	avg, min, max := s.responseStats.Stats()
	setCommonHeaders(ctx)
	ctx.SetStatusCode(fasthttp.StatusOK)
	ctx.SetBody(
		fmt.Appendf(nil, `{"avg_ms":%.2f,"min_ms":%.2f,"max_ms":%.2f}`,
			avg.Seconds()*1000, min.Seconds()*1000, max.Seconds()*1000))
}

func respondWithError(ctx *fasthttp.RequestCtx, statusCode int, errCode int) {
	ctx.SetStatusCode(statusCode)
	json.NewEncoder(ctx).Encode(map[string]interface{}{
		"error": map[string]interface{}{
			"code":    errCode,
			"message": config.ErrorMessages[errCode],
		},
	})
}

func respondWithJSON(ctx *fasthttp.RequestCtx, statusCode int, payload interface{}) {
	ctx.SetStatusCode(statusCode)
	json.NewEncoder(ctx).Encode(payload)
}
